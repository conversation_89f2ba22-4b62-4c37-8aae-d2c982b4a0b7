"""
Fixed training script with better error handling and small dataset support
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import json


class FixedProductDetector:
    def __init__(self, data_dir, img_size=(224, 224), batch_size=8):
        """Initialize with smaller batch size for small datasets"""
        self.data_dir = data_dir
        self.img_size = img_size
        self.batch_size = batch_size
        self.model = None
        self.class_names = []
        self.history = None
        
    def preprocess_image(self, image_path):
        """Preprocess image with better error handling"""
        try:
            # Try OpenCV first
            img = cv2.imread(image_path)
            if img is None:
                print(f"Warning: OpenCV couldn't read {image_path}")
                return None
            
            # Convert BGR to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Resize
            img = cv2.resize(img, self.img_size)
            
            # Normalize to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            return img
            
        except Exception as e:
            print(f"Error preprocessing {image_path}: {e}")
            return None
    
    def load_and_preprocess_data(self):
        """Load data with better validation"""
        print(f"Loading data from: {self.data_dir}")
        
        if not os.path.exists(self.data_dir):
            raise ValueError(f"Data directory not found: {self.data_dir}")
        
        # Get class directories
        self.class_names = [d for d in os.listdir(self.data_dir) 
                           if os.path.isdir(os.path.join(self.data_dir, d))]
        self.class_names.sort()
        
        if len(self.class_names) == 0:
            raise ValueError("No class directories found!")
        
        print(f"Found classes: {self.class_names}")
        
        X = []
        y = []
        
        for class_idx, class_name in enumerate(self.class_names):
            class_dir = os.path.join(self.data_dir, class_name)
            
            # Get image files
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend([f for f in os.listdir(class_dir) 
                                  if f.lower().endswith(ext)])
            
            print(f"Class '{class_name}': {len(image_files)} images")
            
            if len(image_files) == 0:
                print(f"Warning: No images found for class '{class_name}'")
                continue
            
            for img_file in image_files:
                img_path = os.path.join(class_dir, img_file)
                processed_img = self.preprocess_image(img_path)
                
                if processed_img is not None:
                    X.append(processed_img)
                    y.append(class_idx)
                else:
                    print(f"Skipping corrupted image: {img_path}")
        
        if len(X) == 0:
            raise ValueError("No valid images found!")
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"Successfully loaded {len(X)} images")
        print(f"Image shape: {X.shape}")
        print(f"Labels shape: {y.shape}")
        
        return X, y, self.class_names
    
    def create_simple_model(self, num_classes):
        """Create a simpler model for small datasets"""
        model = keras.Sequential([
            # Input layer
            layers.Input(shape=(*self.img_size, 3)),
            
            # First conv block
            layers.Conv2D(16, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Second conv block
            layers.Conv2D(32, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Third conv block
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Global pooling and dense layers
            layers.GlobalAveragePooling2D(),
            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(num_classes, activation='softmax')
        ])
        
        # Compile with lower learning rate for small datasets
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.0001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def train_model(self, X, y, validation_split=0.2, epochs=20):
        """Train with settings optimized for small datasets"""
        print("Creating model...")
        
        num_classes = len(self.class_names)
        self.model = self.create_simple_model(num_classes)
        
        print("Model architecture:")
        self.model.summary()
        
        # For very small datasets, use all data for training
        if len(X) <= 6:
            print("Very small dataset - using all data for training")
            X_train, X_val = X, X
            y_train, y_val = y, y
            validation_split = 0.0
        else:
            # Split manually for better control
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=validation_split, random_state=42, 
                stratify=y if len(np.unique(y)) > 1 else None
            )
        
        print(f"Training samples: {len(X_train)}")
        print(f"Validation samples: {len(X_val)}")
        
        # Callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(
                patience=10, restore_best_weights=True, verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                factor=0.5, patience=5, verbose=1
            )
        ]
        
        # Train the model
        if validation_split > 0:
            self.history = self.model.fit(
                X_train, y_train,
                batch_size=min(self.batch_size, len(X_train)),
                epochs=epochs,
                validation_data=(X_val, y_val),
                callbacks=callbacks,
                verbose=1
            )
        else:
            self.history = self.model.fit(
                X_train, y_train,
                batch_size=min(self.batch_size, len(X_train)),
                epochs=epochs,
                callbacks=callbacks,
                verbose=1
            )
        
        print("Training completed!")
    
    def evaluate_model(self, X_test, y_test):
        """Evaluate the model"""
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        print("Evaluating model...")
        
        # Make predictions
        predictions = self.model.predict(X_test, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y_test)
        print(f"Test Accuracy: {accuracy:.4f}")
        
        # Classification report
        if len(np.unique(y_test)) > 1:
            print("\nClassification Report:")
            print(classification_report(y_test, predicted_classes, 
                                      target_names=self.class_names, zero_division=0))
        
        # Confidence statistics
        confidence_scores = np.max(predictions, axis=1)
        print(f"\nConfidence Statistics:")
        print(f"Mean: {np.mean(confidence_scores):.4f}")
        print(f"Std: {np.std(confidence_scores):.4f}")
        print(f"Min: {np.min(confidence_scores):.4f}")
        print(f"Max: {np.max(confidence_scores):.4f}")
        
        return accuracy, predictions, confidence_scores
    
    def save_model(self, model_path='product_detector_model.h5'):
        """Save the trained model"""
        if self.model is None:
            raise ValueError("No model to save!")
        
        self.model.save(model_path)
        
        # Save configuration
        config = {
            'class_names': self.class_names,
            'img_size': self.img_size,
            'num_classes': len(self.class_names)
        }
        
        with open('model_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Model saved to {model_path}")
        print("Configuration saved to model_config.json")


def main():
    """Main training function with better error handling"""
    print("=== Fixed Product Detection Training ===")
    
    # Configuration
    DATA_DIR = "dataset"
    IMG_SIZE = (224, 224)
    BATCH_SIZE = 4  # Very small batch size
    EPOCHS = 20     # Fewer epochs
    
    print(f"Data directory: {DATA_DIR}")
    print(f"Image size: {IMG_SIZE}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Epochs: {EPOCHS}")
    print()
    
    try:
        # Create detector
        detector = FixedProductDetector(DATA_DIR, IMG_SIZE, BATCH_SIZE)
        
        # Load data
        X, y, class_names = detector.load_and_preprocess_data()
        
        if len(X) < 3:
            print("❌ Dataset too small! Need at least 3 images.")
            print("Consider adding more images or using the test dataset.")
            return
        
        # For very small datasets, use minimal split
        if len(X) <= 6:
            test_size = 1  # Keep 1 image for testing
            X_train, X_test = X[:-test_size], X[-test_size:]
            y_train, y_test = y[:-test_size], y[-test_size:]
        else:
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
        
        print(f"Training set: {len(X_train)} images")
        print(f"Test set: {len(X_test)} images")
        print()
        
        # Train model
        detector.train_model(X_train, y_train, epochs=EPOCHS)
        
        # Evaluate model
        if len(X_test) > 0:
            accuracy, predictions, confidence_scores = detector.evaluate_model(X_test, y_test)
        else:
            print("No test data available - evaluating on training data")
            accuracy, predictions, confidence_scores = detector.evaluate_model(X_train, y_train)
        
        # Save model
        detector.save_model()
        
        print("\n" + "="*50)
        print("✅ TRAINING SUCCESSFUL!")
        print("="*50)
        print(f"Final Accuracy: {accuracy:.4f}")
        print("Files created:")
        print("- product_detector_model.h5")
        print("- model_config.json")
        print("\n🎉 Model ready for production use!")
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        print("\nTroubleshooting:")
        print("1. Run: python debug_dataset.py")
        print("2. Check image files are not corrupted")
        print("3. Ensure at least 3 valid images total")


if __name__ == "__main__":
    main()