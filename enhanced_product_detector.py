"""
Enhanced Product Detector for Conveyor Belt System
- Uses transfer learning for better accuracy
- Includes confidence thresholding for "product not found"
- Optimized for industrial applications
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.applications import EfficientNetB0
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import cv2
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt


class EnhancedProductDetector:
    def __init__(self, data_dir, img_size=(224, 224), batch_size=16, confidence_threshold=0.7):
        """
        Enhanced detector with confidence thresholding
        
        Args:
            data_dir: Directory containing product images
            img_size: Input image size
            batch_size: Training batch size
            confidence_threshold: Minimum confidence for valid detection
        """
        self.data_dir = data_dir
        self.img_size = img_size
        self.batch_size = batch_size
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.class_names = []
        self.history = None
        
    def preprocess_image(self, image_path):
        """Enhanced image preprocessing"""
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                return None
            
            # Convert BGR to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Resize
            img = cv2.resize(img, self.img_size)
            
            # Normalize to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            return img
            
        except Exception as e:
            print(f"Error preprocessing {image_path}: {e}")
            return None
    
    def load_and_preprocess_data(self):
        """Load and preprocess data with validation"""
        print(f"Loading data from: {self.data_dir}")
        
        if not os.path.exists(self.data_dir):
            raise ValueError(f"Data directory not found: {self.data_dir}")
        
        # Get class directories
        self.class_names = [d for d in os.listdir(self.data_dir) 
                           if os.path.isdir(os.path.join(self.data_dir, d))]
        self.class_names.sort()
        
        if len(self.class_names) == 0:
            raise ValueError("No class directories found!")
        
        print(f"Found {len(self.class_names)} product classes: {self.class_names}")
        
        X = []
        y = []
        
        for class_idx, class_name in enumerate(self.class_names):
            class_dir = os.path.join(self.data_dir, class_name)
            
            # Get image files
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend([f for f in os.listdir(class_dir) 
                                  if f.lower().endswith(ext)])
            
            print(f"Class '{class_name}': {len(image_files)} images")
            
            if len(image_files) < 3:
                print(f"Warning: Very few images for class '{class_name}'. Consider adding more.")
            
            loaded_count = 0
            for img_file in image_files:
                img_path = os.path.join(class_dir, img_file)
                processed_img = self.preprocess_image(img_path)
                
                if processed_img is not None:
                    X.append(processed_img)
                    y.append(class_idx)
                    loaded_count += 1
                else:
                    print(f"Skipping corrupted image: {img_path}")
            
            print(f"  Successfully loaded: {loaded_count} images")
        
        if len(X) == 0:
            raise ValueError("No valid images found!")
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"\nDataset Summary:")
        print(f"Total images: {len(X)}")
        print(f"Image shape: {X.shape}")
        print(f"Number of classes: {len(self.class_names)}")
        
        return X, y, self.class_names
    
    def create_transfer_learning_model(self, num_classes):
        """Create model using transfer learning with EfficientNet"""
        print("Creating transfer learning model with EfficientNetB0...")
        
        # Load pre-trained EfficientNetB0
        base_model = EfficientNetB0(
            weights='imagenet',
            include_top=False,
            input_shape=(*self.img_size, 3)
        )
        
        # Freeze base model initially
        base_model.trainable = False
        
        # Add custom classification head
        model = keras.Sequential([
            base_model,
            layers.GlobalAveragePooling2D(),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            layers.Dense(num_classes, activation='softmax')
        ])
        
        # Compile with lower learning rate for transfer learning
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.0001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def create_data_generators(self, X_train, y_train, X_val, y_val):
        """Create data generators with augmentation"""
        # Data augmentation for training
        train_datagen = ImageDataGenerator(
            rotation_range=15,
            width_shift_range=0.1,
            height_shift_range=0.1,
            shear_range=0.1,
            zoom_range=0.1,
            horizontal_flip=True,
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )
        
        # No augmentation for validation
        val_datagen = ImageDataGenerator()
        
        # Create generators
        train_generator = train_datagen.flow(
            X_train, y_train,
            batch_size=self.batch_size,
            shuffle=True
        )
        
        val_generator = val_datagen.flow(
            X_val, y_val,
            batch_size=self.batch_size,
            shuffle=False
        )
        
        return train_generator, val_generator
    
    def train_model(self, X, y, validation_split=0.2, epochs=50):
        """Train the enhanced model"""
        print("Starting enhanced training...")
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=42, 
            stratify=y if len(np.unique(y)) > 1 else None
        )
        
        print(f"Training samples: {len(X_train)}")
        print(f"Validation samples: {len(X_val)}")
        
        # Create model
        num_classes = len(self.class_names)
        self.model = self.create_transfer_learning_model(num_classes)
        
        print("\nModel Architecture:")
        self.model.summary()
        
        # Create data generators
        train_gen, val_gen = self.create_data_generators(X_train, y_train, X_val, y_val)
        
        # Calculate steps
        steps_per_epoch = max(1, len(X_train) // self.batch_size)
        validation_steps = max(1, len(X_val) // self.batch_size)
        
        # Callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(
                patience=15, restore_best_weights=True, verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                factor=0.5, patience=8, min_lr=1e-7, verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'best_model.h5', save_best_only=True, verbose=1
            )
        ]
        
        # Train the model
        print(f"\nTraining for {epochs} epochs...")
        self.history = self.model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            epochs=epochs,
            validation_data=val_gen,
            validation_steps=validation_steps,
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
        
        # Fine-tuning phase
        print("\nStarting fine-tuning phase...")
        self.fine_tune_model(train_gen, val_gen, steps_per_epoch, validation_steps)
    
    def fine_tune_model(self, train_gen, val_gen, steps_per_epoch, validation_steps):
        """Fine-tune the pre-trained layers"""
        # Unfreeze the top layers of the base model
        base_model = self.model.layers[0]
        base_model.trainable = True
        
        # Fine-tune from this layer onwards
        fine_tune_at = len(base_model.layers) - 20
        
        # Freeze all the layers before fine_tune_at
        for layer in base_model.layers[:fine_tune_at]:
            layer.trainable = False
        
        # Recompile with lower learning rate
        self.model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.00001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # Continue training
        fine_tune_epochs = 10
        total_epochs = len(self.history.history['loss']) + fine_tune_epochs
        
        callbacks = [
            keras.callbacks.EarlyStopping(
                patience=5, restore_best_weights=True, verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                factor=0.5, patience=3, min_lr=1e-8, verbose=1
            )
        ]
        
        history_fine = self.model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            epochs=total_epochs,
            initial_epoch=len(self.history.history['loss']),
            validation_data=val_gen,
            validation_steps=validation_steps,
            callbacks=callbacks,
            verbose=1
        )
        
        print("Fine-tuning completed!")

    def predict_with_confidence(self, image_path):
        """
        Predict with confidence thresholding for unknown detection

        Returns:
            dict: {
                'predicted_class': str,
                'confidence': float,
                'is_known_product': bool,
                'all_probabilities': dict
            }
        """
        if self.model is None:
            raise ValueError("Model not trained yet!")

        # Preprocess image
        img = self.preprocess_image(image_path)
        if img is None:
            raise ValueError(f"Could not process image: {image_path}")

        # Make prediction
        img_batch = np.expand_dims(img, axis=0)
        predictions = self.model.predict(img_batch, verbose=0)[0]

        # Get top prediction
        predicted_idx = np.argmax(predictions)
        confidence = float(predictions[predicted_idx])

        # Check if confidence is above threshold
        is_known_product = confidence >= self.confidence_threshold

        if is_known_product:
            predicted_class = self.class_names[predicted_idx]
        else:
            predicted_class = "PRODUCT_NOT_FOUND"

        # Create probability dictionary
        all_probabilities = {
            self.class_names[i]: float(predictions[i])
            for i in range(len(self.class_names))
        }

        return {
            'predicted_class': predicted_class,
            'confidence': confidence,
            'is_known_product': is_known_product,
            'all_probabilities': all_probabilities
        }

    def evaluate_model(self, X_test, y_test):
        """Evaluate model with confidence analysis"""
        if self.model is None:
            raise ValueError("Model not trained yet!")

        print("Evaluating model...")

        # Make predictions
        predictions = self.model.predict(X_test, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        confidence_scores = np.max(predictions, axis=1)

        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y_test)
        print(f"Test Accuracy: {accuracy:.4f}")

        # Confidence-based accuracy
        high_conf_mask = confidence_scores >= self.confidence_threshold
        if np.sum(high_conf_mask) > 0:
            high_conf_accuracy = np.mean(predicted_classes[high_conf_mask] == y_test[high_conf_mask])
            print(f"High Confidence Accuracy (>{self.confidence_threshold}): {high_conf_accuracy:.4f}")
            print(f"High Confidence Predictions: {np.sum(high_conf_mask)}/{len(y_test)}")

        # Classification report
        if len(np.unique(y_test)) > 1:
            print("\nClassification Report:")
            print(classification_report(y_test, predicted_classes,
                                      target_names=self.class_names, zero_division=0))

        # Confidence statistics
        print(f"\nConfidence Statistics:")
        print(f"Mean: {np.mean(confidence_scores):.4f}")
        print(f"Std: {np.std(confidence_scores):.4f}")
        print(f"Min: {np.min(confidence_scores):.4f}")
        print(f"Max: {np.max(confidence_scores):.4f}")
        print(f"Threshold: {self.confidence_threshold}")

        return accuracy, predictions, confidence_scores

    def save_model(self, model_path='enhanced_product_detector.h5'):
        """Save the trained model and configuration"""
        if self.model is None:
            raise ValueError("No model to save!")

        self.model.save(model_path)

        # Save configuration
        config = {
            'class_names': self.class_names,
            'img_size': self.img_size,
            'num_classes': len(self.class_names),
            'confidence_threshold': self.confidence_threshold,
            'model_type': 'enhanced_transfer_learning'
        }

        with open('enhanced_model_config.json', 'w') as f:
            json.dump(config, f, indent=2)

        print(f"Enhanced model saved to {model_path}")
        print("Configuration saved to enhanced_model_config.json")

    def plot_training_history(self):
        """Plot training history"""
        if self.history is None:
            print("No training history available")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

        # Plot accuracy
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        if 'val_accuracy' in self.history.history:
            ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)

        # Plot loss
        ax2.plot(self.history.history['loss'], label='Training Loss')
        if 'val_loss' in self.history.history:
            ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)

        plt.tight_layout()
        plt.savefig('enhanced_training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Training history saved as enhanced_training_history.png")


def main():
    """Main training function for enhanced product detection"""
    print("=== Enhanced Product Detection Training ===")
    print("Features:")
    print("- Transfer Learning with EfficientNetB0")
    print("- Confidence-based unknown detection")
    print("- Data augmentation")
    print("- Fine-tuning")
    print()

    # Configuration
    DATA_DIR = "product_images"
    IMG_SIZE = (224, 224)
    BATCH_SIZE = 16
    EPOCHS = 50
    CONFIDENCE_THRESHOLD = 0.7  # Adjust based on your needs

    print(f"Data directory: {DATA_DIR}")
    print(f"Image size: {IMG_SIZE}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Epochs: {EPOCHS}")
    print(f"Confidence threshold: {CONFIDENCE_THRESHOLD}")
    print()

    try:
        # Create enhanced detector
        detector = EnhancedProductDetector(
            DATA_DIR, IMG_SIZE, BATCH_SIZE, CONFIDENCE_THRESHOLD
        )

        # Load data
        X, y, class_names = detector.load_and_preprocess_data()

        if len(X) < 10:
            print("⚠️  Warning: Small dataset detected!")
            print("For better results, consider adding more images per class.")
            print("Minimum recommended: 20-50 images per product class")

        # Split data for testing
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42,
            stratify=y if len(np.unique(y)) > 1 else None
        )

        print(f"\nData split:")
        print(f"Training set: {len(X_train)} images")
        print(f"Test set: {len(X_test)} images")
        print()

        # Train model
        detector.train_model(X_train, y_train, epochs=EPOCHS)

        # Plot training history
        detector.plot_training_history()

        # Evaluate model
        if len(X_test) > 0:
            accuracy, predictions, confidence_scores = detector.evaluate_model(X_test, y_test)
        else:
            print("No test data - evaluating on training data")
            accuracy, predictions, confidence_scores = detector.evaluate_model(X_train, y_train)

        # Save model
        detector.save_model()

        print("\n" + "="*60)
        print("🎉 ENHANCED TRAINING COMPLETE!")
        print("="*60)
        print(f"Final Accuracy: {accuracy:.4f}")
        print(f"Confidence Threshold: {CONFIDENCE_THRESHOLD}")
        print("\nFiles created:")
        print("- enhanced_product_detector.h5 (trained model)")
        print("- enhanced_model_config.json (configuration)")
        print("- enhanced_training_history.png (training plots)")
        print("\n✅ Enhanced model ready for conveyor belt deployment!")
        print("\nKey Features:")
        print("- Detects unknown products as 'PRODUCT_NOT_FOUND'")
        print("- Higher accuracy with transfer learning")
        print("- Robust to lighting and angle variations")

    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        print("\nTroubleshooting:")
        print("1. Check internet connection (for downloading EfficientNet)")
        print("2. Ensure sufficient memory (transfer learning needs more RAM)")
        print("3. Verify image files are not corrupted")


if __name__ == "__main__":
    main()
