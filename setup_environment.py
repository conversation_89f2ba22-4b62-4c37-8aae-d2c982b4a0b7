"""
Environment Setup Script
This script helps set up the environment and install required packages
"""

import subprocess
import sys
import os


def install_requirements():
    """Install required packages from requirements.txt"""
    try:
        print("Installing required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing packages: {e}")
        return False


def check_gpu_availability():
    """Check if GPU is available for TensorFlow"""
    try:
        import tensorflow as tf
        print(f"TensorFlow version: {tf.__version__}")
        
        if tf.config.list_physical_devices('GPU'):
            print("✓ GPU is available for training")
            for gpu in tf.config.list_physical_devices('GPU'):
                print(f"  - {gpu}")
        else:
            print("⚠ No GPU found. Training will use CPU (slower)")
        
        return True
    except ImportError:
        print("✗ TensorFlow not installed")
        return False


def create_sample_structure():
    """Create sample directory structure for product images"""
    base_dir = "product_images"
    sample_products = ["laptop", "phone", "tablet", "headphones", "camera"]
    
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
        print(f"✓ Created directory: {base_dir}")
        
        for product in sample_products:
            product_dir = os.path.join(base_dir, product)
            os.makedirs(product_dir, exist_ok=True)
            print(f"✓ Created directory: {product_dir}")
        
        print("\nSample directory structure created!")
        print("Please add your product images to the respective folders:")
        for product in sample_products:
            print(f"  - {base_dir}/{product}/")
    else:
        print(f"✓ Directory {base_dir} already exists")


def main():
    """Main setup function"""
    print("=== Product Detector Environment Setup ===\n")
    
    # Install requirements
    if not install_requirements():
        return
    
    print()
    
    # Check GPU
    if not check_gpu_availability():
        return
    
    print()
    
    # Create sample structure
    create_sample_structure()
    
    print("\n=== Setup Complete ===")
    print("Next steps:")
    print("1. Add your product images to the product_images/ folders")
    print("2. Run: python product_detector.py (to train the model)")
    print("3. Run: python production_predictor.py (to test predictions)")


if __name__ == "__main__":
    main()