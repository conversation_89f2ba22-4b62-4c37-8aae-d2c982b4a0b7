@echo off
echo ===================================
echo Simple Product Detection Training
echo ===================================
echo.

REM Try to find Python executable
set PYTHON_FOUND=0

echo Searching for Python...

REM Try common Python locations
if exist "C:\Python313\python.exe" (
    set PYTHON_CMD=C:\Python313\python.exe
    set PYTHON_FOUND=1
    echo Found Python at C:\Python313\python.exe
    goto :install_packages
)

if exist "C:\Python311\python.exe" (
    set PYTHON_CMD=C:\Python311\python.exe
    set PYTHON_FOUND=1
    echo Found Python at C:\Python311\python.exe
    goto :install_packages
)

REM Try py launcher
py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    echo Found Python launcher (py)
    goto :install_packages
)

REM Try python command
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    echo Found Python in PATH
    goto :install_packages
)

if %PYTHON_FOUND% equ 0 (
    echo ❌ Python not found!
    echo.
    echo Please install Python from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

:install_packages
echo.
echo Installing required packages...
%PYTHON_CMD% -m pip install tensorflow opencv-python numpy matplotlib --quiet
if %errorlevel% neq 0 (
    echo ❌ Failed to install packages
    echo Trying alternative installation...
    %PYTHON_CMD% -m pip install tensorflow-cpu opencv-python numpy matplotlib --quiet
    if %errorlevel% neq 0 (
        echo ❌ Package installation failed
        pause
        exit /b 1
    )
)

echo ✓ Packages installed successfully!
echo.

:check_dataset
echo Checking dataset...
if not exist "dataset" (
    echo ❌ Dataset folder not found!
    echo Please ensure you have a 'dataset' folder with product subfolders
    pause
    exit /b 1
)

echo ✓ Dataset folder found
echo.

:train_model
echo Starting simple training...
echo.
%PYTHON_CMD% simple_train.py
if %errorlevel% neq 0 (
    echo ❌ Training failed
    echo.
    echo Troubleshooting tips:
    echo 1. Check that dataset folder contains product subfolders
    echo 2. Ensure image files are not corrupted
    echo 3. Try running: %PYTHON_CMD% debug_dataset.py
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Training completed successfully!
echo.
echo Generated files:
echo - product_detector_model.h5 (trained model)
echo - model_config.json (configuration)
echo.
echo Your model is ready for use!
echo.
pause