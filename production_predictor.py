"""
Production Product Predictor
This module provides a simple interface for using the trained model in production
to predict product classes from images.
"""

import os
import json
import numpy as np
from product_detector import ProductDetector


class ProductionPredictor:
    def __init__(self, model_path='product_detector_model.h5'):
        """
        Initialize the production predictor
        
        Args:
            model_path (str): Path to the trained model H5 file
        """
        self.detector = ProductDetector(data_dir="", img_size=(224, 224))
        self.detector.load_model(model_path)
        
    def predict(self, image_path, threshold=0.5):
        """
        Predict product class for an image
        
        Args:
            image_path (str): Path to the image file
            threshold (float): Minimum confidence threshold for prediction
            
        Returns:
            dict: Prediction results with class, confidence, and all probabilities
        """
        try:
            results = self.detector.predict_single_image(image_path)
            
            # Add threshold check
            results['above_threshold'] = results['confidence'] >= threshold
            results['threshold_used'] = threshold
            
            return results
            
        except Exception as e:
            return {
                'error': str(e),
                'predicted_class': None,
                'confidence': 0.0,
                'above_threshold': False
            }
    
    def batch_predict(self, image_paths, threshold=0.5):
        """
        Predict product classes for multiple images
        
        Args:
            image_paths (list): List of image file paths
            threshold (float): Minimum confidence threshold for prediction
            
        Returns:
            list: List of prediction results
        """
        results = []
        for img_path in image_paths:
            result = self.predict(img_path, threshold)
            result['image_path'] = img_path
            results.append(result)
        return results


def main():
    """Example usage of the production predictor"""
    # Check if model exists
    model_path = 'product_detector_model.h5'
    if not os.path.exists(model_path):
        print(f"Model file '{model_path}' not found!")
        print("Please train the model first by running: python product_detector.py")
        return
    
    # Initialize predictor
    print("Loading trained model...")
    predictor = ProductionPredictor(model_path)
    
    # Example prediction (you can modify this path)
    test_image = "test_image.jpg"  # Replace with actual test image path
    
    if os.path.exists(test_image):
        print(f"Predicting for image: {test_image}")
        result = predictor.predict(test_image)
        
        print(f"Predicted Class: {result['predicted_class']}")
        print(f"Confidence: {result['confidence']:.4f}")
        print(f"Above Threshold: {result['above_threshold']}")
        print("\nAll Predictions:")
        for class_name, prob in result['all_predictions'].items():
            print(f"  {class_name}: {prob:.4f}")
    else:
        print(f"Test image '{test_image}' not found.")
        print("Please provide a valid image path for testing.")


if __name__ == "__main__":
    main()