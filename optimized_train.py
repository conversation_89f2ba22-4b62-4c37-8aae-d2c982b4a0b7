"""
Optimized training script for better accuracy with small datasets
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.callbacks import ReduceLROnPlateau, EarlyStopping
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import cv2
import json
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split

# Disable GPU warnings
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

def load_images_optimized():
    """Optimized image loading with better preprocessing"""
    print("Loading images from dataset...")

    dataset_dir = "product_images"
    images = []
    labels = []
    class_names = []
    
    # Get class directories
    for item in os.listdir(dataset_dir):
        class_path = os.path.join(dataset_dir, item)
        if os.path.isdir(class_path):
            class_names.append(item)
    
    class_names.sort()
    print(f"Found classes: {class_names}")
    
    # Load images with enhanced preprocessing
    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(dataset_dir, class_name)
        print(f"Loading images from: {class_name}")
        
        for file in os.listdir(class_path):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                img_path = os.path.join(class_path, file)
                print(f"  Loading: {file}")
                
                try:
                    # Load image
                    img = cv2.imread(img_path)
                    if img is not None:
                        # Convert BGR to RGB
                        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        # Resize to 224x224
                        img = cv2.resize(img, (224, 224))
                        
                        # Enhanced preprocessing for better feature extraction
                        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
                        lab = cv2.cvtColor(img, cv2.COLOR_RGB2LAB)
                        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
                        lab[:,:,0] = clahe.apply(lab[:,:,0])
                        img = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
                        
                        # Normalize
                        img = img.astype(np.float32) / 255.0
                        
                        images.append(img)
                        labels.append(class_idx)
                        print(f"    ✓ Loaded and enhanced: {img.shape}")
                    else:
                        print(f"    ✗ Failed to load: {file}")
                except Exception as e:
                    print(f"    ✗ Error loading {file}: {e}")
    
    if len(images) == 0:
        raise ValueError("No images loaded!")
    
    X = np.array(images)
    y = np.array(labels)
    
    print(f"\nDataset summary:")
    print(f"  Total images: {len(X)}")
    print(f"  Image shape: {X.shape}")
    print(f"  Classes: {len(class_names)}")
    print(f"  Class distribution: {np.bincount(y)}")
    
    return X, y, class_names

def create_optimized_model(num_classes):
    """Create a model optimized for small datasets"""
    model = keras.Sequential([
        layers.Input(shape=(224, 224, 3)),
        
        # First block - smaller filters, less aggressive pooling
        layers.Conv2D(16, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.Conv2D(16, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.2),
        
        # Second block
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.2),
        
        # Third block
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.3),
        
        # Global pooling instead of flatten to reduce parameters
        layers.GlobalAveragePooling2D(),
        
        # Smaller dense layers to prevent overfitting
        layers.Dense(128, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        
        layers.Dense(64, activation='relu'),
        layers.Dropout(0.5),
        
        # Output layer
        layers.Dense(num_classes, activation='softmax')
    ])
    
    # Use a lower learning rate for better convergence
    model.compile(
        optimizer=keras.optimizers.Adam(learning_rate=0.0005),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def create_data_augmentation():
    """Create data augmentation for small datasets"""
    return ImageDataGenerator(
        rotation_range=30,
        width_shift_range=0.3,
        height_shift_range=0.3,
        shear_range=0.2,
        zoom_range=0.3,
        horizontal_flip=True,
        vertical_flip=False,
        fill_mode='nearest',
        brightness_range=[0.8, 1.2],
        channel_shift_range=0.1
    )

def main():
    """Main training function with improved parameters"""
    print("=== Optimized Product Detection Training ===")
    
    try:
        # Load data
        X, y, class_names = load_images_optimized()
        
        # For very small datasets, use different strategy
        if len(X) <= 10:
            print("\n⚠️  Very small dataset detected. Using specialized training approach...")
            
            # Create model
            print("\nCreating optimized model...")
            model = create_optimized_model(len(class_names))
            model.summary()
            
            # Create data augmentation
            datagen = create_data_augmentation()
            
            # Use all data for training with heavy augmentation
            print(f"\nTraining with heavy data augmentation...")
            
            # Callbacks for better training
            callbacks = [
                ReduceLROnPlateau(
                    monitor='loss',
                    factor=0.5,
                    patience=10,
                    min_lr=1e-7,
                    verbose=1
                ),
                EarlyStopping(
                    monitor='loss',
                    patience=25,
                    restore_best_weights=True,
                    verbose=1
                )
            ]
            
            # Train with augmented data
            batch_size = 2  # Very small batch size
            steps_per_epoch = max(20, len(X) * 5)  # More steps with augmentation
            
            history = model.fit(
                datagen.flow(X, y, batch_size=batch_size),
                steps_per_epoch=steps_per_epoch,
                epochs=100,  # More epochs but with early stopping
                callbacks=callbacks,
                verbose=1
            )
            
        else:
            # Standard training for larger datasets
            print("\nUsing standard training approach...")
            
            # Split data
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Create model
            model = create_optimized_model(len(class_names))
            model.summary()
            
            # Train model
            history = model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                batch_size=min(4, len(X_train)),
                epochs=50,
                verbose=1
            )
        
        # Test predictions on all data
        print("\nTesting final model performance...")
        predictions = model.predict(X, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y)
        print(f"Final Training Accuracy: {accuracy:.4f}")
        
        # Show detailed predictions
        print("\nDetailed prediction results:")
        for i in range(len(X)):
            true_class = class_names[y[i]]
            pred_class = class_names[predicted_classes[i]]
            confidence = predictions[i][predicted_classes[i]]
            status = "✓" if predicted_classes[i] == y[i] else "✗"
            print(f"  {status} Image {i+1}: True={true_class}, Pred={pred_class}, Conf={confidence:.4f}")
        
        # Plot training history
        if len(history.history['loss']) > 1:
            plt.figure(figsize=(12, 4))
            
            plt.subplot(1, 2, 1)
            plt.plot(history.history['loss'], label='Training Loss')
            if 'val_loss' in history.history:
                plt.plot(history.history['val_loss'], label='Validation Loss')
            plt.title('Model Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()
            plt.grid(True)
            
            plt.subplot(1, 2, 2)
            plt.plot(history.history['accuracy'], label='Training Accuracy')
            if 'val_accuracy' in history.history:
                plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
            plt.title('Model Accuracy')
            plt.xlabel('Epoch')
            plt.ylabel('Accuracy')
            plt.legend()
            plt.grid(True)
            
            plt.tight_layout()
            plt.savefig('improved_training_history.png', dpi=300, bbox_inches='tight')
            plt.show()
        
        # Save model
        print("\nSaving optimized model...")
        model.save('product_detector_model.h5')
        
        # Save config
        config = {
            'class_names': class_names,
            'img_size': [224, 224],
            'num_classes': len(class_names)
        }
        
        with open('model_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Optimized training completed successfully!")
        print(f"📊 Final Accuracy: {accuracy:.4f}")
        print("📁 Files created:")
        print("  - product_detector_model.h5 (optimized model)")
        print("  - model_config.json (configuration)")
        print("  - improved_training_history.png (training plots)")
        
        return True
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()