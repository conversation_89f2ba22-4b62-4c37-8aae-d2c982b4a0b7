# Product Detection System

A comprehensive machine learning project for detecting and classifying products in images using TensorFlow/Keras and Convolutional Neural Networks (CNN).

## Features

- **Image Preprocessing**: Automatic image resizing, normalization, and histogram equalization
- **CNN Architecture**: Custom CNN model with batch normalization and dropout for robust classification
- **Data Augmentation**: Real-time data augmentation during training to improve model generalization
- **Confidence Statistics**: Detailed confidence metrics and visualization
- **Production Ready**: H5 model export for production deployment
- **Comprehensive Evaluation**: Confusion matrix, classification reports, and training history plots

## Project Structure

```
product-detector/
├── product_detector.py          # Main training and evaluation module
├── production_predictor.py      # Production inference module
├── setup_environment.py         # Environment setup script
├── requirements.txt             # Python dependencies
├── README.md                    # This file
├── product_images/              # Root directory for training data
│   ├── product1/               # Folder for product 1 images
│   ├── product2/               # Folder for product 2 images
│   └── ...                     # Additional product folders
└── outputs/                     # Generated files (models, plots, etc.)
    ├── product_detector_model.h5
    ├── model_config.json
    ├── confusion_matrix.png
    ├── confidence_distribution.png
    └── training_history.png
```

## Installation

1. **Clone or download this project**

2. **Install dependencies**:
   ```bash
   python setup_environment.py
   ```
   
   Or manually:
   ```bash
   pip install -r requirements.txt
   ```

## Data Preparation

1. **Create product folders**: Organize your images in the following structure:
   ```
   product_images/
   ├── laptop/
   │   ├── laptop1.jpg
   │   ├── laptop2.jpg
   │   └── ...
   ├── phone/
   │   ├── phone1.jpg
   │   ├── phone2.jpg
   │   └── ...
   └── tablet/
       ├── tablet1.jpg
       ├── tablet2.jpg
       └── ...
   ```

2. **Image requirements**:
   - Supported formats: PNG, JPG, JPEG, BMP, TIFF
   - Any resolution (images will be resized to 224x224)
   - Minimum 50-100 images per product class recommended

## Usage

### Training the Model

```bash
python product_detector.py
```

This will:
- Load and preprocess all images
- Create train/validation/test splits
- Train the CNN model with data augmentation
- Evaluate the model and show statistics
- Save the trained model as `product_detector_model.h5`
- Generate visualization plots

### Production Inference

```bash
python production_predictor.py
```

Or use programmatically:

```python
from production_predictor import ProductionPredictor

# Initialize predictor
predictor = ProductionPredictor('product_detector_model.h5')

# Predict single image
result = predictor.predict('path/to/image.jpg')
print(f"Product: {result['predicted_class']}")
print(f"Confidence: {result['confidence']:.4f}")

# Batch prediction
results = predictor.batch_predict(['img1.jpg', 'img2.jpg'])
```

## Model Architecture

The CNN model includes:
- **4 Convolutional blocks** with increasing filters (32→64→128→256)
- **Batch normalization** after each conv layer
- **MaxPooling** for spatial dimension reduction
- **Dropout layers** for regularization
- **Global Average Pooling** instead of flatten
- **Dense layers** with dropout for classification
- **Softmax output** for multi-class probability

## Key Features

### Image Preprocessing
- Automatic resizing to uniform dimensions
- Pixel normalization to [0,1] range
- Histogram equalization for better contrast
- BGR to RGB conversion

### Training Features
- **Data Augmentation**: Rotation, shifting, flipping, zooming
- **Early Stopping**: Prevents overfitting
- **Learning Rate Reduction**: Adaptive learning rate
- **Model Checkpointing**: Saves best model during training

### Evaluation Metrics
- Accuracy and loss curves
- Confusion matrix visualization
- Classification report (precision, recall, F1-score)
- Confidence score distribution
- Per-class performance statistics

## Configuration

Key parameters in `product_detector.py`:

```python
DATA_DIR = "product_images"    # Root folder for training data
IMG_SIZE = (224, 224)         # Target image size
BATCH_SIZE = 32               # Training batch size
EPOCHS = 50                   # Number of training epochs
```

## Output Files

After training, the following files are generated:

- `product_detector_model.h5`: Trained model for production use
- `model_config.json`: Model configuration and class names
- `confusion_matrix.png`: Confusion matrix visualization
- `confidence_distribution.png`: Confidence score distribution
- `training_history.png`: Training/validation accuracy and loss curves

## Production Deployment

The trained model can be deployed in production:

1. **Load the model**: Use `ProductionPredictor` class
2. **Process images**: Automatic preprocessing pipeline
3. **Get predictions**: Returns class, confidence, and all probabilities
4. **Set thresholds**: Configure minimum confidence for predictions

## Requirements

- Python 3.7+
- TensorFlow 2.13+
- OpenCV 4.8+
- NumPy, Matplotlib, Scikit-learn
- See `requirements.txt` for complete list

## GPU Support

The system automatically detects and uses GPU if available. For GPU acceleration:
- Install CUDA-compatible TensorFlow
- Ensure CUDA and cuDNN are properly installed
- Run `python setup_environment.py` to check GPU availability

## Troubleshooting

**Common Issues:**

1. **"No product folders found"**: Ensure `product_images/` contains subdirectories with images
2. **"Could not read image"**: Check image file formats and corruption
3. **Low accuracy**: Increase dataset size, adjust model architecture, or tune hyperparameters
4. **Memory errors**: Reduce batch size or image resolution

## License

This project is open source and available under the MIT License.