"""
FINAL CONVEYOR BELT SOLUTION
- Template matching + ML hybrid approach
- Works with small datasets
- Reliable unknown product detection
- Industrial conveyor belt ready
"""

import os
import cv2
import numpy as np
import json
from datetime import datetime
import tensorflow as tf
from tensorflow import keras
from sklearn.model_selection import train_test_split


class FinalConveyorDetector:
    def __init__(self, data_dir="test-data", confidence_threshold=0.3):
        """
        Final solution combining template matching with ML
        
        Args:
            data_dir: Directory containing product images
            confidence_threshold: Lower threshold for small datasets
        """
        self.data_dir = data_dir
        self.confidence_threshold = confidence_threshold
        self.class_names = []
        self.templates = {}  # Store template images for each class
        self.model = None
        
        # Statistics
        self.product_counts = {}
        self.unknown_count = 0
        self.total_detections = 0
        self.session_start_time = datetime.now()
        
        # Load templates and train model
        self.load_templates()
        self.train_simple_model()
    
    def load_templates(self):
        """Load template images for each product class"""
        print("Loading product templates...")
        
        if not os.path.exists(self.data_dir):
            raise ValueError(f"Data directory not found: {self.data_dir}")
        
        # Get class directories
        self.class_names = [d for d in os.listdir(self.data_dir) 
                           if os.path.isdir(os.path.join(self.data_dir, d))]
        self.class_names.sort()
        
        print(f"Found {len(self.class_names)} product classes: {self.class_names}")
        
        # Load templates for each class
        for class_name in self.class_names:
            class_dir = os.path.join(self.data_dir, class_name)
            images = [f for f in os.listdir(class_dir) 
                     if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            if images:
                # Use first image as template
                template_path = os.path.join(class_dir, images[0])
                template = cv2.imread(template_path)
                if template is not None:
                    # Resize template to standard size
                    template = cv2.resize(template, (100, 100))
                    self.templates[class_name] = template
                    print(f"  ✅ Template loaded for {class_name}")
            
            # Initialize product count
            self.product_counts[class_name] = 0
        
        print(f"✅ Loaded {len(self.templates)} product templates")
    
    def train_simple_model(self):
        """Train a simple model as backup"""
        print("\nTraining backup ML model...")
        
        try:
            X = []
            y = []
            
            # Load all images
            for class_idx, class_name in enumerate(self.class_names):
                class_dir = os.path.join(self.data_dir, class_name)
                images = [f for f in os.listdir(class_dir) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                
                for img_file in images:
                    img_path = os.path.join(class_dir, img_file)
                    img = cv2.imread(img_path)
                    if img is not None:
                        # Simple preprocessing
                        img = cv2.resize(img, (64, 64))
                        img = img.astype(np.float32) / 255.0
                        X.append(img)
                        y.append(class_idx)
            
            if len(X) > 10:  # Only train if we have enough data
                X = np.array(X)
                y = np.array(y)
                
                # Simple model
                model = keras.Sequential([
                    keras.layers.Input(shape=(64, 64, 3)),
                    keras.layers.Conv2D(8, (3, 3), activation='relu'),
                    keras.layers.MaxPooling2D((2, 2)),
                    keras.layers.Conv2D(16, (3, 3), activation='relu'),
                    keras.layers.MaxPooling2D((2, 2)),
                    keras.layers.GlobalAveragePooling2D(),
                    keras.layers.Dense(16, activation='relu'),
                    keras.layers.Dense(len(self.class_names), activation='softmax')
                ])
                
                model.compile(
                    optimizer='adam',
                    loss='sparse_categorical_crossentropy',
                    metrics=['accuracy']
                )
                
                # Train quickly
                model.fit(X, y, epochs=20, verbose=0, batch_size=4)
                self.model = model
                print("✅ Backup ML model trained")
            else:
                print("⚠️  Not enough data for ML model, using template matching only")
                
        except Exception as e:
            print(f"⚠️  ML model training failed: {e}")
            print("Using template matching only")
    
    def template_match_score(self, image, template):
        """Calculate template matching score"""
        try:
            # Resize image to template size
            img_resized = cv2.resize(image, (100, 100))
            
            # Convert to grayscale for matching
            img_gray = cv2.cvtColor(img_resized, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            
            # Template matching
            result = cv2.matchTemplate(img_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, _ = cv2.minMaxLoc(result)
            
            return max_val
            
        except Exception as e:
            return 0.0
    
    def ml_predict(self, image):
        """Get ML prediction if model is available"""
        if self.model is None:
            return None, 0.0
        
        try:
            # Preprocess for ML model
            img = cv2.resize(image, (64, 64))
            img = img.astype(np.float32) / 255.0
            img_batch = np.expand_dims(img, axis=0)
            
            # Predict
            predictions = self.model.predict(img_batch, verbose=0)[0]
            predicted_idx = np.argmax(predictions)
            confidence = float(predictions[predicted_idx])
            
            return predicted_idx, confidence
            
        except Exception as e:
            return None, 0.0
    
    def detect_product(self, image_path):
        """
        Detect product using hybrid approach
        
        Args:
            image_path: Path to image or OpenCV image
            
        Returns:
            dict: Detection result
        """
        try:
            # Load image
            if isinstance(image_path, str):
                image = cv2.imread(image_path)
                if image is None:
                    return {'success': False, 'error': 'Could not load image'}
            else:
                image = image_path.copy()
            
            best_match = "PRODUCT_NOT_FOUND"
            best_score = 0.0
            all_scores = {}
            
            # Template matching
            for class_name, template in self.templates.items():
                score = self.template_match_score(image, template)
                all_scores[f"{class_name}_template"] = score
                
                if score > best_score:
                    best_score = score
                    best_match = class_name
            
            # ML prediction (if available)
            ml_class_idx, ml_confidence = self.ml_predict(image)
            if ml_class_idx is not None:
                ml_class = self.class_names[ml_class_idx]
                all_scores[f"{ml_class}_ml"] = ml_confidence
                
                # Combine template and ML scores
                if ml_confidence > best_score * 0.8:  # ML gets slight preference
                    best_score = ml_confidence
                    best_match = ml_class
            
            # Determine if product is known
            is_known_product = best_score >= self.confidence_threshold
            
            if not is_known_product:
                best_match = "PRODUCT_NOT_FOUND"
                status = "UNKNOWN"
            else:
                status = "DETECTED"
            
            # Update statistics
            self.update_statistics(best_match, is_known_product)
            
            return {
                'success': True,
                'product': best_match,
                'confidence': best_score,
                'status': status,
                'is_known_product': is_known_product,
                'all_scores': all_scores,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Detection failed: {str(e)}'
            }
    
    def update_statistics(self, predicted_class, is_known_product):
        """Update detection statistics"""
        self.total_detections += 1
        
        if is_known_product:
            self.product_counts[predicted_class] += 1
        else:
            self.unknown_count += 1
    
    def print_statistics(self):
        """Print current statistics"""
        session_duration = datetime.now() - self.session_start_time
        
        print("\n" + "="*50)
        print("📊 FINAL CONVEYOR BELT STATISTICS")
        print("="*50)
        print(f"Session Duration: {session_duration}")
        print(f"Total Detections: {self.total_detections}")
        print(f"Detection Rate: {self.total_detections / max(1, session_duration.total_seconds()) * 60:.1f} per minute")
        print(f"Known Product Rate: {(self.total_detections - self.unknown_count) / max(1, self.total_detections) * 100:.1f}%")
        print()
        
        print("Product Counts:")
        for product, count in self.product_counts.items():
            print(f"  {product}: {count}")
        
        print(f"Unknown Products: {self.unknown_count}")
        print("="*50)
    
    def adjust_threshold(self, new_threshold):
        """Adjust confidence threshold"""
        old_threshold = self.confidence_threshold
        self.confidence_threshold = new_threshold
        print(f"🎯 Threshold adjusted: {old_threshold:.2f} → {new_threshold:.2f}")
    
    def save_config(self):
        """Save configuration"""
        config = {
            'class_names': self.class_names,
            'confidence_threshold': self.confidence_threshold,
            'model_type': 'final_hybrid_detector'
        }
        
        with open('final_conveyor_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Configuration saved to final_conveyor_config.json")


def test_final_system():
    """Test the final detection system"""
    print("🧪 Testing Final Conveyor Belt System")
    print("-" * 40)
    
    try:
        # Initialize detector
        detector = FinalConveyorDetector()
        
        print(f"\n🔍 Testing with known products...")
        
        # Test each product class
        for class_name in detector.class_names:
            class_path = os.path.join("test-data", class_name)
            if os.path.isdir(class_path):
                images = [f for f in os.listdir(class_path) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                
                if images:
                    # Test first image
                    test_image = os.path.join(class_path, images[0])
                    print(f"\n📦 Testing: {class_name}")
                    
                    result = detector.detect_product(test_image)
                    
                    if result['success']:
                        print(f"   ✅ Detected: {result['product']}")
                        print(f"   📊 Confidence: {result['confidence']:.4f}")
                        print(f"   🎯 Status: {result['status']}")
                        
                        # Check accuracy
                        if result['is_known_product']:
                            if any(word in result['product'] for word in class_name.split()) or any(word in class_name for word in result['product'].split()):
                                print(f"   ✅ CORRECT DETECTION!")
                            else:
                                print(f"   ⚠️  MISCLASSIFICATION")
                        else:
                            print(f"   ❌ FALSE NEGATIVE")
                    else:
                        print(f"   ❌ Detection failed: {result.get('error', 'Unknown error')}")
        
        # Test unknown detection
        print("\n\n🎲 Testing unknown product detection...")
        random_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        result = detector.detect_product(random_image)
        if result['success']:
            print(f"   🔍 Result: {result['product']}")
            print(f"   📊 Confidence: {result['confidence']:.4f}")
            
            if result['product'] == "PRODUCT_NOT_FOUND":
                print(f"   ✅ CORRECT: Unknown properly detected!")
            else:
                print(f"   ⚠️  WARNING: Random noise classified as product")
        
        # Show statistics
        detector.print_statistics()
        
        # Save configuration
        detector.save_config()
        
        print("\n✅ Final system test completed!")
        print("\n🎯 Recommendations:")
        print("- Current threshold: 0.3 (adjust with detector.adjust_threshold())")
        print("- Add more images per product for better accuracy")
        print("- Use consistent lighting on conveyor belt")
        print("- Mount camera at fixed position")
        
        return detector
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main function"""
    print("🏭 FINAL CONVEYOR BELT PRODUCT DETECTION SYSTEM")
    print("="*55)
    print("🚀 Hybrid Approach: Template Matching + Machine Learning")
    print("✅ Works with small datasets")
    print("🔍 Reliable unknown product detection")
    print("⚡ Fast inference for real-time use")
    print()
    
    # Run test
    detector = test_final_system()
    
    if detector:
        print("\n🎉 SYSTEM READY FOR DEPLOYMENT!")
        print("\n📋 Usage Instructions:")
        print("```python")
        print("from FINAL_CONVEYOR_SOLUTION import FinalConveyorDetector")
        print("")
        print("# Initialize")
        print("detector = FinalConveyorDetector()")
        print("")
        print("# Detect product")
        print("result = detector.detect_product('image.jpg')")
        print("print(f'Product: {result[\"product\"]}')") 
        print("print(f'Confidence: {result[\"confidence\"]:.4f}')")
        print("```")


if __name__ == "__main__":
    main()
