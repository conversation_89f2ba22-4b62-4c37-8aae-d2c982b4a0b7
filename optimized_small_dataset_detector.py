"""
Optimized Product Detector for Small Datasets
- Designed specifically for small datasets (50-100 images)
- Includes confidence thresholding for unknown detection
- Optimized architecture to prevent overfitting
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import cv2
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import matplotlib.pyplot as plt


class OptimizedSmallDatasetDetector:
    def __init__(self, data_dir, img_size=(128, 128), batch_size=8, confidence_threshold=0.6):
        """
        Optimized detector for small datasets
        
        Args:
            data_dir: Directory containing product images
            img_size: Smaller input size for faster training
            batch_size: Small batch size for small datasets
            confidence_threshold: Lower threshold for small datasets
        """
        self.data_dir = data_dir
        self.img_size = img_size
        self.batch_size = batch_size
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.class_names = []
        self.history = None
        
    def preprocess_image(self, image_path):
        """Enhanced preprocessing for small datasets"""
        try:
            if isinstance(image_path, str):
                img = cv2.imread(image_path)
                if img is None:
                    return None
            else:
                img = image_path.copy()
            
            # Convert BGR to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Resize
            img = cv2.resize(img, self.img_size)
            
            # Enhanced preprocessing for better feature extraction
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            lab = cv2.cvtColor(img, cv2.COLOR_RGB2LAB)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            lab[:,:,0] = clahe.apply(lab[:,:,0])
            img = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
            
            # Normalize to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            return img
            
        except Exception as e:
            print(f"Error preprocessing image: {e}")
            return None
    
    def load_and_preprocess_data(self):
        """Load data with enhanced validation"""
        print(f"Loading data from: {self.data_dir}")
        
        if not os.path.exists(self.data_dir):
            raise ValueError(f"Data directory not found: {self.data_dir}")
        
        # Get class directories
        self.class_names = [d for d in os.listdir(self.data_dir) 
                           if os.path.isdir(os.path.join(self.data_dir, d))]
        self.class_names.sort()
        
        if len(self.class_names) == 0:
            raise ValueError("No class directories found!")
        
        print(f"Found {len(self.class_names)} product classes: {self.class_names}")
        
        X = []
        y = []
        
        for class_idx, class_name in enumerate(self.class_names):
            class_dir = os.path.join(self.data_dir, class_name)
            
            # Get image files
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend([f for f in os.listdir(class_dir) 
                                  if f.lower().endswith(ext)])
            
            print(f"Class '{class_name}': {len(image_files)} images")
            
            loaded_count = 0
            for img_file in image_files:
                img_path = os.path.join(class_dir, img_file)
                processed_img = self.preprocess_image(img_path)
                
                if processed_img is not None:
                    X.append(processed_img)
                    y.append(class_idx)
                    loaded_count += 1
                else:
                    print(f"Skipping corrupted image: {img_path}")
            
            print(f"  Successfully loaded: {loaded_count} images")
        
        if len(X) == 0:
            raise ValueError("No valid images found!")
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"\nDataset Summary:")
        print(f"Total images: {len(X)}")
        print(f"Image shape: {X.shape}")
        print(f"Number of classes: {len(self.class_names)}")
        
        return X, y, self.class_names
    
    def create_optimized_model(self, num_classes):
        """Create optimized model for small datasets"""
        print("Creating optimized model for small datasets...")
        
        model = keras.Sequential([
            # Input layer
            layers.Input(shape=(*self.img_size, 3)),
            
            # First conv block - smaller filters
            layers.Conv2D(16, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.2),
            
            # Second conv block
            layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.3),
            
            # Third conv block
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.3),
            
            # Global pooling to reduce parameters
            layers.GlobalAveragePooling2D(),
            
            # Small dense layers to prevent overfitting
            layers.Dense(64, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            # Output layer
            layers.Dense(num_classes, activation='softmax')
        ])
        
        # Compile with appropriate settings for small datasets
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def create_data_generators(self, X_train, y_train, X_val, y_val):
        """Create data generators with aggressive augmentation for small datasets"""
        # Aggressive data augmentation for small datasets
        train_datagen = ImageDataGenerator(
            rotation_range=30,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.3,
            horizontal_flip=True,
            vertical_flip=False,
            brightness_range=[0.7, 1.3],
            channel_shift_range=0.2,
            fill_mode='nearest'
        )
        
        # No augmentation for validation
        val_datagen = ImageDataGenerator()
        
        # Create generators
        train_generator = train_datagen.flow(
            X_train, y_train,
            batch_size=self.batch_size,
            shuffle=True
        )
        
        val_generator = val_datagen.flow(
            X_val, y_val,
            batch_size=self.batch_size,
            shuffle=False
        )
        
        return train_generator, val_generator
    
    def train_model(self, X, y, validation_split=0.2, epochs=100):
        """Train with settings optimized for small datasets"""
        print("Starting optimized training for small datasets...")
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=42, 
            stratify=y if len(np.unique(y)) > 1 else None
        )
        
        print(f"Training samples: {len(X_train)}")
        print(f"Validation samples: {len(X_val)}")
        
        # Create model
        num_classes = len(self.class_names)
        self.model = self.create_optimized_model(num_classes)
        
        print("\nModel Architecture:")
        self.model.summary()
        
        # Create data generators
        train_gen, val_gen = self.create_data_generators(X_train, y_train, X_val, y_val)
        
        # Calculate steps - ensure at least 1
        steps_per_epoch = max(1, len(X_train) // self.batch_size)
        validation_steps = max(1, len(X_val) // self.batch_size)
        
        # Multiply steps for data augmentation effect
        steps_per_epoch = steps_per_epoch * 10  # 10x more steps due to augmentation
        
        # Callbacks optimized for small datasets
        callbacks = [
            keras.callbacks.EarlyStopping(
                patience=20, restore_best_weights=True, verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                factor=0.5, patience=10, min_lr=1e-7, verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'best_optimized_model.h5', save_best_only=True, verbose=1
            )
        ]
        
        # Train the model
        print(f"\nTraining for {epochs} epochs with {steps_per_epoch} steps per epoch...")
        self.history = self.model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            epochs=epochs,
            validation_data=val_gen,
            validation_steps=validation_steps,
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
    
    def predict_with_confidence(self, image_path):
        """Predict with confidence thresholding"""
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        # Preprocess image
        img = self.preprocess_image(image_path)
        if img is None:
            raise ValueError(f"Could not process image: {image_path}")
        
        # Make prediction
        img_batch = np.expand_dims(img, axis=0)
        predictions = self.model.predict(img_batch, verbose=0)[0]
        
        # Get top prediction
        predicted_idx = np.argmax(predictions)
        confidence = float(predictions[predicted_idx])
        
        # Check if confidence is above threshold
        is_known_product = confidence >= self.confidence_threshold
        
        if is_known_product:
            predicted_class = self.class_names[predicted_idx]
        else:
            predicted_class = "PRODUCT_NOT_FOUND"
        
        # Create probability dictionary
        all_probabilities = {
            self.class_names[i]: float(predictions[i]) 
            for i in range(len(self.class_names))
        }
        
        return {
            'predicted_class': predicted_class,
            'confidence': confidence,
            'is_known_product': is_known_product,
            'all_probabilities': all_probabilities
        }
    
    def evaluate_model(self, X_test, y_test):
        """Evaluate model with confidence analysis"""
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        print("Evaluating model...")
        
        # Make predictions
        predictions = self.model.predict(X_test, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        confidence_scores = np.max(predictions, axis=1)
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y_test)
        print(f"Test Accuracy: {accuracy:.4f}")
        
        # Confidence-based accuracy
        high_conf_mask = confidence_scores >= self.confidence_threshold
        if np.sum(high_conf_mask) > 0:
            high_conf_accuracy = np.mean(predicted_classes[high_conf_mask] == y_test[high_conf_mask])
            print(f"High Confidence Accuracy (>{self.confidence_threshold}): {high_conf_accuracy:.4f}")
            print(f"High Confidence Predictions: {np.sum(high_conf_mask)}/{len(y_test)}")
        
        # Classification report
        if len(np.unique(y_test)) > 1:
            print("\nClassification Report:")
            print(classification_report(y_test, predicted_classes, 
                                      target_names=self.class_names, zero_division=0))
        
        # Confidence statistics
        print(f"\nConfidence Statistics:")
        print(f"Mean: {np.mean(confidence_scores):.4f}")
        print(f"Std: {np.std(confidence_scores):.4f}")
        print(f"Min: {np.min(confidence_scores):.4f}")
        print(f"Max: {np.max(confidence_scores):.4f}")
        print(f"Threshold: {self.confidence_threshold}")
        
        return accuracy, predictions, confidence_scores
    
    def save_model(self, model_path='optimized_product_detector.h5'):
        """Save the trained model and configuration"""
        if self.model is None:
            raise ValueError("No model to save!")
        
        self.model.save(model_path)
        
        # Save configuration
        config = {
            'class_names': self.class_names,
            'img_size': self.img_size,
            'num_classes': len(self.class_names),
            'confidence_threshold': self.confidence_threshold,
            'model_type': 'optimized_small_dataset'
        }
        
        with open('optimized_model_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Optimized model saved to {model_path}")
        print("Configuration saved to optimized_model_config.json")

    def plot_training_history(self):
        """Plot training history"""
        if self.history is None:
            print("No training history available")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

        # Plot accuracy
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        if 'val_accuracy' in self.history.history:
            ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy (Optimized)')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)

        # Plot loss
        ax2.plot(self.history.history['loss'], label='Training Loss')
        if 'val_loss' in self.history.history:
            ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss (Optimized)')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)

        plt.tight_layout()
        plt.savefig('optimized_training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Training history saved as optimized_training_history.png")


def main():
    """Main training function for optimized small dataset detection"""
    print("=== Optimized Small Dataset Product Detection ===")
    print("Features:")
    print("- Optimized architecture for small datasets")
    print("- Aggressive data augmentation")
    print("- Confidence-based unknown detection")
    print("- Enhanced preprocessing with CLAHE")
    print()

    # Configuration optimized for small datasets
    DATA_DIR = "product_images"
    IMG_SIZE = (128, 128)  # Smaller size for faster training
    BATCH_SIZE = 8         # Small batch size
    EPOCHS = 100           # More epochs with early stopping
    CONFIDENCE_THRESHOLD = 0.6  # Lower threshold for small datasets

    print(f"Data directory: {DATA_DIR}")
    print(f"Image size: {IMG_SIZE}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Epochs: {EPOCHS}")
    print(f"Confidence threshold: {CONFIDENCE_THRESHOLD}")
    print()

    try:
        # Create optimized detector
        detector = OptimizedSmallDatasetDetector(
            DATA_DIR, IMG_SIZE, BATCH_SIZE, CONFIDENCE_THRESHOLD
        )

        # Load data
        X, y, class_names = detector.load_and_preprocess_data()

        print(f"\n📊 Dataset Analysis:")
        print(f"Total images: {len(X)}")
        print(f"Images per class: {len(X) // len(class_names):.1f} average")

        if len(X) < 30:
            print("⚠️  Very small dataset! Results may vary.")
            print("💡 Recommendation: Add more images for better accuracy")

        # Split data for testing
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42,
            stratify=y if len(np.unique(y)) > 1 else None
        )

        print(f"\nData split:")
        print(f"Training set: {len(X_train)} images")
        print(f"Test set: {len(X_test)} images")
        print()

        # Train model
        detector.train_model(X_train, y_train, epochs=EPOCHS)

        # Plot training history
        detector.plot_training_history()

        # Evaluate model
        if len(X_test) > 0:
            accuracy, predictions, confidence_scores = detector.evaluate_model(X_test, y_test)
        else:
            print("No test data - evaluating on training data")
            accuracy, predictions, confidence_scores = detector.evaluate_model(X_train, y_train)

        # Save model
        detector.save_model()

        print("\n" + "="*60)
        print("🎉 OPTIMIZED TRAINING COMPLETE!")
        print("="*60)
        print(f"Final Accuracy: {accuracy:.4f}")
        print(f"Confidence Threshold: {CONFIDENCE_THRESHOLD}")
        print("\nFiles created:")
        print("- optimized_product_detector.h5 (optimized model)")
        print("- optimized_model_config.json (configuration)")
        print("- optimized_training_history.png (training plots)")
        print("- best_optimized_model.h5 (best checkpoint)")
        print("\n✅ Optimized model ready for conveyor belt deployment!")
        print("\nKey Features:")
        print("- Better accuracy on small datasets")
        print("- Detects unknown products as 'PRODUCT_NOT_FOUND'")
        print("- Enhanced preprocessing for better features")
        print("- Aggressive augmentation for robustness")

    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        print("\nTroubleshooting:")
        print("1. Ensure sufficient memory")
        print("2. Check image files are not corrupted")
        print("3. Verify at least 3 images per class")


if __name__ == "__main__":
    main()
