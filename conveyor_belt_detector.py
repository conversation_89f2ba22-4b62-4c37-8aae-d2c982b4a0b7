"""
Conveyor Belt Product Detection System
- Real-time product detection and counting
- Unknown product detection
- Logging and statistics
"""

import os
import cv2
import numpy as np
import tensorflow as tf
import json
import time
from datetime import datetime
import threading
import queue


class ConveyorBeltDetector:
    def __init__(self, model_path='enhanced_product_detector.h5', config_path='enhanced_model_config.json'):
        """
        Initialize conveyor belt detector
        
        Args:
            model_path: Path to trained model
            config_path: Path to model configuration
        """
        self.model = None
        self.config = None
        self.class_names = []
        self.img_size = (224, 224)
        self.confidence_threshold = 0.7
        
        # Statistics
        self.product_counts = {}
        self.unknown_count = 0
        self.total_detections = 0
        self.session_start_time = datetime.now()
        
        # Load model and config
        self.load_model(model_path, config_path)
        
        # Initialize product counts
        for class_name in self.class_names:
            self.product_counts[class_name] = 0
    
    def load_model(self, model_path, config_path):
        """Load the trained model and configuration"""
        try:
            # Load configuration
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    self.config = json.load(f)
                
                self.class_names = self.config['class_names']
                self.img_size = tuple(self.config['img_size'])
                self.confidence_threshold = self.config.get('confidence_threshold', 0.7)
                
                print(f"✅ Configuration loaded:")
                print(f"   Classes: {self.class_names}")
                print(f"   Confidence threshold: {self.confidence_threshold}")
            else:
                raise FileNotFoundError(f"Configuration file not found: {config_path}")
            
            # Load model
            if os.path.exists(model_path):
                self.model = tf.keras.models.load_model(model_path)
                print(f"✅ Model loaded from: {model_path}")
            else:
                raise FileNotFoundError(f"Model file not found: {model_path}")
                
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            raise
    
    def preprocess_image(self, image):
        """
        Preprocess image for prediction
        
        Args:
            image: OpenCV image (BGR format) or image path
            
        Returns:
            Preprocessed image array
        """
        try:
            # Handle both image path and image array
            if isinstance(image, str):
                img = cv2.imread(image)
                if img is None:
                    raise ValueError(f"Could not load image: {image}")
            else:
                img = image.copy()
            
            # Convert BGR to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Resize to model input size
            img = cv2.resize(img, self.img_size)
            
            # Normalize to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            return img
            
        except Exception as e:
            print(f"Error preprocessing image: {e}")
            return None
    
    def detect_product(self, image):
        """
        Detect product in image
        
        Args:
            image: OpenCV image or image path
            
        Returns:
            dict: Detection result with product info
        """
        if self.model is None:
            raise ValueError("Model not loaded!")
        
        # Preprocess image
        processed_img = self.preprocess_image(image)
        if processed_img is None:
            return {
                'success': False,
                'error': 'Image preprocessing failed'
            }
        
        try:
            # Make prediction
            img_batch = np.expand_dims(processed_img, axis=0)
            predictions = self.model.predict(img_batch, verbose=0)[0]
            
            # Get top prediction
            predicted_idx = np.argmax(predictions)
            confidence = float(predictions[predicted_idx])
            
            # Determine if product is known
            is_known_product = confidence >= self.confidence_threshold
            
            if is_known_product:
                predicted_class = self.class_names[predicted_idx]
                status = "DETECTED"
            else:
                predicted_class = "PRODUCT_NOT_FOUND"
                status = "UNKNOWN"
            
            # Create probability dictionary
            all_probabilities = {
                self.class_names[i]: float(predictions[i]) 
                for i in range(len(self.class_names))
            }
            
            # Update statistics
            self.update_statistics(predicted_class, is_known_product)
            
            return {
                'success': True,
                'product': predicted_class,
                'confidence': confidence,
                'status': status,
                'is_known_product': is_known_product,
                'all_probabilities': all_probabilities,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Prediction failed: {str(e)}'
            }
    
    def update_statistics(self, predicted_class, is_known_product):
        """Update detection statistics"""
        self.total_detections += 1
        
        if is_known_product:
            self.product_counts[predicted_class] += 1
        else:
            self.unknown_count += 1
    
    def get_statistics(self):
        """Get current detection statistics"""
        session_duration = datetime.now() - self.session_start_time
        
        return {
            'session_duration': str(session_duration),
            'total_detections': self.total_detections,
            'product_counts': self.product_counts.copy(),
            'unknown_count': self.unknown_count,
            'detection_rate': self.total_detections / max(1, session_duration.total_seconds()) * 60,  # per minute
            'known_product_rate': (self.total_detections - self.unknown_count) / max(1, self.total_detections) * 100
        }
    
    def reset_statistics(self):
        """Reset all statistics"""
        for class_name in self.class_names:
            self.product_counts[class_name] = 0
        self.unknown_count = 0
        self.total_detections = 0
        self.session_start_time = datetime.now()
        print("📊 Statistics reset")
    
    def print_statistics(self):
        """Print current statistics"""
        stats = self.get_statistics()
        
        print("\n" + "="*50)
        print("📊 CONVEYOR BELT DETECTION STATISTICS")
        print("="*50)
        print(f"Session Duration: {stats['session_duration']}")
        print(f"Total Detections: {stats['total_detections']}")
        print(f"Detection Rate: {stats['detection_rate']:.1f} per minute")
        print(f"Known Product Rate: {stats['known_product_rate']:.1f}%")
        print()
        
        print("Product Counts:")
        for product, count in stats['product_counts'].items():
            print(f"  {product}: {count}")
        
        print(f"Unknown Products: {stats['unknown_count']}")
        print("="*50)
    
    def save_detection_log(self, detection_result, image_path=None):
        """Save detection result to log file"""
        log_entry = {
            'timestamp': detection_result.get('timestamp', datetime.now().isoformat()),
            'product': detection_result.get('product', 'ERROR'),
            'confidence': detection_result.get('confidence', 0.0),
            'status': detection_result.get('status', 'ERROR'),
            'image_path': image_path
        }
        
        # Append to log file
        log_file = f"detection_log_{datetime.now().strftime('%Y%m%d')}.json"
        
        try:
            # Read existing log
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    log_data = json.load(f)
            else:
                log_data = []
            
            # Add new entry
            log_data.append(log_entry)
            
            # Write back to file
            with open(log_file, 'w') as f:
                json.dump(log_data, f, indent=2)
                
        except Exception as e:
            print(f"Warning: Could not save to log file: {e}")


def test_single_image(detector, image_path):
    """Test detection on a single image"""
    print(f"\n🔍 Testing image: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return
    
    # Detect product
    result = detector.detect_product(image_path)
    
    if result['success']:
        print(f"✅ Detection Result:")
        print(f"   Product: {result['product']}")
        print(f"   Confidence: {result['confidence']:.4f}")
        print(f"   Status: {result['status']}")
        
        if result['is_known_product']:
            print(f"   ✅ Known product detected!")
        else:
            print(f"   ⚠️  Unknown product - not in database")
        
        # Save to log
        detector.save_detection_log(result, image_path)
        
    else:
        print(f"❌ Detection failed: {result.get('error', 'Unknown error')}")


def main():
    """Main function for testing the conveyor belt detector"""
    print("🏭 Conveyor Belt Product Detection System")
    print("="*50)
    
    try:
        # Initialize detector
        detector = ConveyorBeltDetector()
        
        print("\n🎯 System ready for product detection!")
        print("Features:")
        print("- Real-time product identification")
        print("- Unknown product detection")
        print("- Automatic counting and logging")
        print("- Detection statistics")
        
        # Test with sample images from dataset
        print("\n🧪 Testing with sample images...")
        
        # Find some test images
        test_dir = "product_images"
        if os.path.exists(test_dir):
            for class_name in os.listdir(test_dir):
                class_path = os.path.join(test_dir, class_name)
                if os.path.isdir(class_path):
                    images = [f for f in os.listdir(class_path) 
                             if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    if images:
                        # Test first image from this class
                        test_image = os.path.join(class_path, images[0])
                        test_single_image(detector, test_image)
                        break
        
        # Show statistics
        detector.print_statistics()
        
        print("\n✅ System test completed!")
        print("Ready for conveyor belt deployment!")
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
