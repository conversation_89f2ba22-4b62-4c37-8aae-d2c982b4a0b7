"""
Simplified training script that works with very small datasets
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import cv2
import json

# Disable GPU warnings and set memory growth
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
tf.config.run_functions_eagerly(True)  # Enable eager execution for debugging

def load_images_simple():
    """Simple image loading function"""
    print("Loading images from dataset...")
    
    dataset_dir = "dataset"
    images = []
    labels = []
    class_names = []
    
    # Get class directories
    for item in os.listdir(dataset_dir):
        class_path = os.path.join(dataset_dir, item)
        if os.path.isdir(class_path):
            class_names.append(item)
    
    class_names.sort()
    print(f"Found classes: {class_names}")
    
    # Load images
    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(dataset_dir, class_name)
        print(f"Loading images from: {class_name}")
        
        for file in os.listdir(class_path):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                img_path = os.path.join(class_path, file)
                print(f"  Loading: {file}")
                
                try:
                    # Load image
                    img = cv2.imread(img_path)
                    if img is not None:
                        # Convert BGR to RGB
                        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        # Resize to 224x224
                        img = cv2.resize(img, (224, 224))
                        # Normalize
                        img = img.astype(np.float32) / 255.0
                        
                        images.append(img)
                        labels.append(class_idx)
                        print(f"    ✓ Loaded: {img.shape}")
                    else:
                        print(f"    ✗ Failed to load: {file}")
                except Exception as e:
                    print(f"    ✗ Error loading {file}: {e}")
    
    if len(images) == 0:
        raise ValueError("No images loaded!")
    
    X = np.array(images)
    y = np.array(labels)
    
    print(f"\nDataset summary:")
    print(f"  Total images: {len(X)}")
    print(f"  Image shape: {X.shape}")
    print(f"  Classes: {len(class_names)}")
    print(f"  Class distribution: {np.bincount(y)}")
    
    return X, y, class_names

def create_simple_model(num_classes):
    """Create a very simple model for small datasets"""
    model = keras.Sequential([
        layers.Input(shape=(224, 224, 3)),
        
        # Simple conv layers
        layers.Conv2D(8, (5, 5), activation='relu'),
        layers.MaxPooling2D((4, 4)),
        layers.Dropout(0.25),
        
        layers.Conv2D(16, (3, 3), activation='relu'),
        layers.MaxPooling2D((4, 4)),
        layers.Dropout(0.25),
        
        layers.Conv2D(32, (3, 3), activation='relu'),
        layers.GlobalAveragePooling2D(),
        
        # Dense layers
        layers.Dense(64, activation='relu'),
        layers.Dropout(0.5),
        layers.Dense(num_classes, activation='softmax')
    ])
    
    model.compile(
        optimizer=keras.optimizers.Adam(learning_rate=0.001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def main():
    """Main training function"""
    print("=== Simple Product Detection Training ===")
    
    try:
        # Load data
        X, y, class_names = load_images_simple()
        
        # Create model
        print("\nCreating model...")
        model = create_simple_model(len(class_names))
        model.summary()
        
        # For very small datasets, train on all data
        print(f"\nTraining with {len(X)} images...")
        
        # Use small batch size
        batch_size = min(2, len(X))
        
        # Train model
        history = model.fit(
            X, y,
            batch_size=batch_size,
            epochs=10,  # Few epochs for small dataset
            verbose=1,
            shuffle=True
        )
        
        # Test predictions
        print("\nTesting predictions...")
        predictions = model.predict(X, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y)
        print(f"Training Accuracy: {accuracy:.4f}")
        
        # Show predictions for each image
        print("\nPrediction details:")
        for i in range(len(X)):
            true_class = class_names[y[i]]
            pred_class = class_names[predicted_classes[i]]
            confidence = predictions[i][predicted_classes[i]]
            print(f"  Image {i+1}: True={true_class}, Pred={pred_class}, Conf={confidence:.4f}")
        
        # Save model
        print("\nSaving model...")
        model.save('product_detector_model.h5')
        
        # Save config
        config = {
            'class_names': class_names,
            'img_size': [224, 224],
            'num_classes': len(class_names)
        }
        
        with open('model_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Training completed successfully!")
        print("Files created:")
        print("  - product_detector_model.h5")
        print("  - model_config.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()