"""
Practical Conveyor Belt Product Detector
- Designed specifically for your 60-image dataset
- Simple but effective architecture
- Confidence-based unknown detection
- Real-world industrial application ready
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import cv2
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import matplotlib.pyplot as plt
from datetime import datetime


class PracticalConveyorDetector:
    def __init__(self, data_dir, img_size=(96, 96), batch_size=4, confidence_threshold=0.5):
        """
        Practical detector optimized for your specific use case
        
        Args:
            data_dir: Directory containing product images
            img_size: Compact input size for efficiency
            batch_size: Very small batch size for small datasets
            confidence_threshold: Conservative threshold
        """
        self.data_dir = data_dir
        self.img_size = img_size
        self.batch_size = batch_size
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.class_names = []
        self.history = None
        
        # Statistics for conveyor belt
        self.product_counts = {}
        self.unknown_count = 0
        self.total_detections = 0
        self.session_start_time = datetime.now()
        
    def preprocess_image(self, image_path):
        """Simple but effective preprocessing"""
        try:
            if isinstance(image_path, str):
                img = cv2.imread(image_path)
                if img is None:
                    return None
            else:
                img = image_path.copy()
            
            # Convert BGR to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Resize
            img = cv2.resize(img, self.img_size)
            
            # Simple normalization
            img = img.astype(np.float32) / 255.0
            
            return img
            
        except Exception as e:
            print(f"Error preprocessing image: {e}")
            return None
    
    def load_and_preprocess_data(self):
        """Load data with validation"""
        print(f"Loading data from: {self.data_dir}")
        
        if not os.path.exists(self.data_dir):
            raise ValueError(f"Data directory not found: {self.data_dir}")
        
        # Get class directories
        self.class_names = [d for d in os.listdir(self.data_dir) 
                           if os.path.isdir(os.path.join(self.data_dir, d))]
        self.class_names.sort()
        
        if len(self.class_names) == 0:
            raise ValueError("No class directories found!")
        
        print(f"Found {len(self.class_names)} product classes: {self.class_names}")
        
        X = []
        y = []
        
        for class_idx, class_name in enumerate(self.class_names):
            class_dir = os.path.join(self.data_dir, class_name)
            
            # Get image files
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend([f for f in os.listdir(class_dir) 
                                  if f.lower().endswith(ext)])
            
            print(f"Class '{class_name}': {len(image_files)} images")
            
            loaded_count = 0
            for img_file in image_files:
                img_path = os.path.join(class_dir, img_file)
                processed_img = self.preprocess_image(img_path)
                
                if processed_img is not None:
                    X.append(processed_img)
                    y.append(class_idx)
                    loaded_count += 1
                else:
                    print(f"Skipping corrupted image: {img_path}")
            
            print(f"  Successfully loaded: {loaded_count} images")
        
        if len(X) == 0:
            raise ValueError("No valid images found!")
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"\nDataset Summary:")
        print(f"Total images: {len(X)}")
        print(f"Image shape: {X.shape}")
        print(f"Number of classes: {len(self.class_names)}")
        
        # Initialize product counts
        for class_name in self.class_names:
            self.product_counts[class_name] = 0
        
        return X, y, self.class_names
    
    def create_practical_model(self, num_classes):
        """Create a practical model that works well with small datasets"""
        print("Creating practical model for small datasets...")
        
        model = keras.Sequential([
            # Input layer
            layers.Input(shape=(*self.img_size, 3)),
            
            # Simple but effective architecture
            layers.Conv2D(8, (5, 5), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            layers.Conv2D(16, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Global pooling
            layers.GlobalAveragePooling2D(),
            
            # Simple classifier
            layers.Dense(32, activation='relu'),
            layers.Dropout(0.5),
            layers.Dense(num_classes, activation='softmax')
        ])
        
        # Compile with conservative settings
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def train_model(self, X, y, validation_split=0.2, epochs=50):
        """Train with practical settings"""
        print("Starting practical training...")
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=42, 
            stratify=y if len(np.unique(y)) > 1 else None
        )
        
        print(f"Training samples: {len(X_train)}")
        print(f"Validation samples: {len(X_val)}")
        
        # Create model
        num_classes = len(self.class_names)
        self.model = self.create_practical_model(num_classes)
        
        print("\nModel Architecture:")
        self.model.summary()
        
        # Data augmentation
        datagen = ImageDataGenerator(
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            horizontal_flip=True,
            zoom_range=0.2,
            fill_mode='nearest'
        )
        
        # Fit the generator
        datagen.fit(X_train)
        
        # Callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(
                patience=15, restore_best_weights=True, verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                factor=0.5, patience=8, min_lr=1e-6, verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'best_practical_model.h5', save_best_only=True, verbose=1
            )
        ]
        
        # Calculate steps properly for small dataset
        steps_per_epoch = max(1, len(X_train) // self.batch_size)
        
        # Train the model
        print(f"\nTraining for {epochs} epochs with {steps_per_epoch} steps per epoch...")
        self.history = self.model.fit(
            datagen.flow(X_train, y_train, batch_size=self.batch_size),
            steps_per_epoch=steps_per_epoch,
            epochs=epochs,
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
    
    def detect_product(self, image):
        """Detect product with confidence thresholding"""
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        # Preprocess image
        processed_img = self.preprocess_image(image)
        if processed_img is None:
            return {
                'success': False,
                'error': 'Image preprocessing failed'
            }
        
        try:
            # Make prediction
            img_batch = np.expand_dims(processed_img, axis=0)
            predictions = self.model.predict(img_batch, verbose=0)[0]
            
            # Get top prediction
            predicted_idx = np.argmax(predictions)
            confidence = float(predictions[predicted_idx])
            
            # Determine if product is known
            is_known_product = confidence >= self.confidence_threshold
            
            if is_known_product:
                predicted_class = self.class_names[predicted_idx]
                status = "DETECTED"
            else:
                predicted_class = "PRODUCT_NOT_FOUND"
                status = "UNKNOWN"
            
            # Update statistics
            self.update_statistics(predicted_class, is_known_product)
            
            return {
                'success': True,
                'product': predicted_class,
                'confidence': confidence,
                'status': status,
                'is_known_product': is_known_product,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Prediction failed: {str(e)}'
            }
    
    def update_statistics(self, predicted_class, is_known_product):
        """Update detection statistics"""
        self.total_detections += 1
        
        if is_known_product:
            self.product_counts[predicted_class] += 1
        else:
            self.unknown_count += 1
    
    def print_statistics(self):
        """Print current statistics"""
        session_duration = datetime.now() - self.session_start_time
        
        print("\n" + "="*50)
        print("📊 PRACTICAL CONVEYOR BELT STATISTICS")
        print("="*50)
        print(f"Session Duration: {session_duration}")
        print(f"Total Detections: {self.total_detections}")
        print(f"Detection Rate: {self.total_detections / max(1, session_duration.total_seconds()) * 60:.1f} per minute")
        print(f"Known Product Rate: {(self.total_detections - self.unknown_count) / max(1, self.total_detections) * 100:.1f}%")
        print()
        
        print("Product Counts:")
        for product, count in self.product_counts.items():
            print(f"  {product}: {count}")
        
        print(f"Unknown Products: {self.unknown_count}")
        print("="*50)
    
    def evaluate_model(self, X_test, y_test):
        """Evaluate model"""
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        print("Evaluating model...")
        
        # Make predictions
        predictions = self.model.predict(X_test, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        confidence_scores = np.max(predictions, axis=1)
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y_test)
        print(f"Test Accuracy: {accuracy:.4f}")
        
        # Confidence-based accuracy
        high_conf_mask = confidence_scores >= self.confidence_threshold
        if np.sum(high_conf_mask) > 0:
            high_conf_accuracy = np.mean(predicted_classes[high_conf_mask] == y_test[high_conf_mask])
            print(f"High Confidence Accuracy (>{self.confidence_threshold}): {high_conf_accuracy:.4f}")
            print(f"High Confidence Predictions: {np.sum(high_conf_mask)}/{len(y_test)}")
        
        # Classification report
        if len(np.unique(y_test)) > 1:
            print("\nClassification Report:")
            print(classification_report(y_test, predicted_classes, 
                                      target_names=self.class_names, zero_division=0))
        
        # Confidence statistics
        print(f"\nConfidence Statistics:")
        print(f"Mean: {np.mean(confidence_scores):.4f}")
        print(f"Std: {np.std(confidence_scores):.4f}")
        print(f"Min: {np.min(confidence_scores):.4f}")
        print(f"Max: {np.max(confidence_scores):.4f}")
        print(f"Threshold: {self.confidence_threshold}")
        
        return accuracy, predictions, confidence_scores
    
    def save_model(self, model_path='practical_conveyor_detector.h5'):
        """Save the trained model and configuration"""
        if self.model is None:
            raise ValueError("No model to save!")
        
        self.model.save(model_path)
        
        # Save configuration
        config = {
            'class_names': self.class_names,
            'img_size': self.img_size,
            'num_classes': len(self.class_names),
            'confidence_threshold': self.confidence_threshold,
            'model_type': 'practical_conveyor_detector'
        }
        
        with open('practical_model_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Practical model saved to {model_path}")
        print("Configuration saved to practical_model_config.json")


def test_practical_system():
    """Test the practical detection system"""
    print("🧪 Testing Practical Conveyor Belt System")
    print("-" * 40)

    # Load model if it exists
    if not os.path.exists('practical_conveyor_detector.h5'):
        print("❌ Model not found! Please train first.")
        return

    # Load configuration
    with open('practical_model_config.json', 'r') as f:
        config = json.load(f)

    # Create detector instance
    detector = PracticalConveyorDetector("product_images")
    detector.model = keras.models.load_model('practical_conveyor_detector.h5')
    detector.class_names = config['class_names']
    detector.img_size = tuple(config['img_size'])
    detector.confidence_threshold = config['confidence_threshold']

    # Initialize product counts
    for class_name in detector.class_names:
        detector.product_counts[class_name] = 0

    print(f"✅ Model loaded with {len(detector.class_names)} classes")
    print(f"Confidence threshold: {detector.confidence_threshold}")

    # Test with sample images
    test_dir = "product_images"
    if os.path.exists(test_dir):
        print("\n🔍 Testing with known products...")

        for class_name in os.listdir(test_dir):
            class_path = os.path.join(test_dir, class_name)
            if os.path.isdir(class_path):
                images = [f for f in os.listdir(class_path)
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

                if images:
                    # Test first image from this class
                    test_image = os.path.join(class_path, images[0])
                    print(f"\n📦 Testing: {class_name}")

                    result = detector.detect_product(test_image)

                    if result['success']:
                        print(f"   ✅ Detected: {result['product']}")
                        print(f"   📊 Confidence: {result['confidence']:.4f}")
                        print(f"   🎯 Status: {result['status']}")

                        # Check accuracy
                        if result['is_known_product']:
                            if any(word in result['product'] for word in class_name.split()) or any(word in class_name for word in result['product'].split()):
                                print(f"   ✅ CORRECT DETECTION!")
                            else:
                                print(f"   ⚠️  MISCLASSIFICATION")
                        else:
                            print(f"   ❌ FALSE NEGATIVE")
                    else:
                        print(f"   ❌ Detection failed: {result.get('error', 'Unknown error')}")

    # Test unknown detection
    print("\n\n🎲 Testing unknown product detection...")
    random_image = np.random.randint(0, 255, (96, 96, 3), dtype=np.uint8)

    result = detector.detect_product(random_image)
    if result['success']:
        print(f"   🔍 Result: {result['product']}")
        print(f"   📊 Confidence: {result['confidence']:.4f}")

        if result['product'] == "PRODUCT_NOT_FOUND":
            print(f"   ✅ CORRECT: Unknown properly detected!")
        else:
            print(f"   ⚠️  WARNING: Random noise classified as product")

    # Show statistics
    detector.print_statistics()

    print("\n✅ Practical system test completed!")


def main():
    """Main training function for practical conveyor detection"""
    print("=== Practical Conveyor Belt Product Detection ===")
    print("Features:")
    print("- Simple but effective architecture")
    print("- Optimized for 60-image dataset")
    print("- Confidence-based unknown detection")
    print("- Fast inference (96x96 input)")
    print("- Industrial conveyor belt ready")
    print()

    # Configuration
    DATA_DIR = "product_images"
    IMG_SIZE = (96, 96)
    BATCH_SIZE = 4
    EPOCHS = 50
    CONFIDENCE_THRESHOLD = 0.5

    print(f"Data directory: {DATA_DIR}")
    print(f"Image size: {IMG_SIZE}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Epochs: {EPOCHS}")
    print(f"Confidence threshold: {CONFIDENCE_THRESHOLD}")
    print()

    try:
        # Create detector
        detector = PracticalConveyorDetector(
            DATA_DIR, IMG_SIZE, BATCH_SIZE, CONFIDENCE_THRESHOLD
        )

        # Load data
        X, y, class_names = detector.load_and_preprocess_data()

        print(f"\n📊 Dataset Analysis:")
        print(f"Total images: {len(X)}")
        print(f"Images per class: {len(X) // len(class_names):.1f} average")
        print("✅ Good dataset size for practical training!")

        # Split data for testing
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42,
            stratify=y if len(np.unique(y)) > 1 else None
        )

        print(f"\nData split:")
        print(f"Training set: {len(X_train)} images")
        print(f"Test set: {len(X_test)} images")
        print()

        # Train model
        detector.train_model(X_train, y_train, epochs=EPOCHS)

        # Evaluate model
        if len(X_test) > 0:
            accuracy, predictions, confidence_scores = detector.evaluate_model(X_test, y_test)
        else:
            print("No test data - evaluating on training data")
            accuracy, predictions, confidence_scores = detector.evaluate_model(X_train, y_train)

        # Save model
        detector.save_model()

        print("\n" + "="*60)
        print("🎉 PRACTICAL TRAINING COMPLETE!")
        print("="*60)
        print(f"Final Accuracy: {accuracy:.4f}")
        print(f"Confidence Threshold: {CONFIDENCE_THRESHOLD}")
        print("\nFiles created:")
        print("- practical_conveyor_detector.h5 (practical model)")
        print("- practical_model_config.json (configuration)")
        print("- best_practical_model.h5 (best checkpoint)")
        print("\n✅ Practical model ready for conveyor belt deployment!")
        print("\nKey Features:")
        print("- Optimized for your 60-image dataset")
        print("- Fast inference for real-time detection")
        print("- Reliable unknown product detection")
        print("- Simple architecture that actually works")

        # Run test
        print("\n" + "="*40)
        print("🧪 RUNNING SYSTEM TEST")
        print("="*40)
        test_practical_system()

    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
