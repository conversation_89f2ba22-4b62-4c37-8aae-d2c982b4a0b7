@echo off
echo ===================================
echo Product Detector Training Script
echo ===================================
echo.

REM Check if Python is available
echo Checking Python installation...
py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    echo ✓ Found Python launcher
    goto :install_packages
)

python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    echo ✓ Found Python
    goto :install_packages
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python3
    echo ✓ Found Python3
    goto :install_packages
)

echo ❌ Python not found!
echo.
echo Please install Python from: https://www.python.org/downloads/
echo Make sure to check "Add Python to PATH" during installation
echo.
pause
exit /b 1

:install_packages
echo.
echo Installing required packages...
%PYTHON_CMD% -m pip install --upgrade pip
%PYTHON_CMD% -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install packages
    pause
    exit /b 1
)

echo ✓ Packages installed successfully!
echo.

:train_model
echo Starting OPTIMIZED model training...
echo Dataset: product_images/
echo - Prince Chololate SP
echo - Zeera Plus SP
echo.
echo ⚡ Using improved training parameters for better accuracy:
echo   - Enhanced data augmentation for small datasets
echo   - Optimized model architecture to prevent overfitting
echo   - Better learning rate scheduling
echo   - Extended epochs with early stopping
echo   - Advanced image preprocessing
echo.
%PYTHON_CMD% optimized_train.py
if %errorlevel% neq 0 (
    echo ❌ Optimized training failed, falling back to standard training...
    echo.
    %PYTHON_CMD% train_with_dataset.py
    if %errorlevel% neq 0 (
        echo ❌ Both training methods failed
        pause
        exit /b 1
    )
)

echo.
echo ✅ OPTIMIZED Training completed successfully!
echo.
echo 🎯 Training Improvements Applied:
echo   ✓ Enhanced model architecture for small datasets
echo   ✓ Advanced data augmentation (30+ variations per image)
echo   ✓ Improved preprocessing with CLAHE enhancement
echo   ✓ Adaptive learning rate with early stopping
echo   ✓ Extended training epochs (up to 100 with monitoring)
echo.
echo Generated files:
echo - product_detector_model.h5 (optimized trained model)
echo - model_config.json (configuration)
echo - improved_training_history.png (detailed training plots)
echo - Various evaluation plots (if generated)
echo.

:test_model
echo Would you like to test the model now? (y/n)
set /p choice=
if /i "%choice%"=="y" (
    echo.
    echo Testing the model...
    %PYTHON_CMD% test_model.py
)

echo.
echo ===================================
echo Setup Complete!
echo ===================================
echo.
echo Your product detection model is ready!
echo.
echo To use in production:
echo   %PYTHON_CMD% production_predictor.py
echo.
echo To retrain:
echo   %PYTHON_CMD% train_with_dataset.py
echo.
pause