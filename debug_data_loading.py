"""
Debug script to check data loading and identify issues
"""

import os
import numpy as np
from product_detector import ProductDetector

def debug_data_loading():
    """Debug the data loading process"""
    print("=== Data Loading Debug ===")
    
    DATA_DIR = "product_images"
    IMG_SIZE = (224, 224)
    BATCH_SIZE = 4
    
    print(f"Checking directory: {DATA_DIR}")
    
    # Check if directory exists
    if not os.path.exists(DATA_DIR):
        print(f"❌ Directory {DATA_DIR} does not exist!")
        return False
    
    # List contents
    print(f"✓ Directory exists")
    contents = os.listdir(DATA_DIR)
    print(f"Contents: {contents}")
    
    # Check subdirectories
    class_dirs = [d for d in contents if os.path.isdir(os.path.join(DATA_DIR, d))]
    print(f"Class directories: {class_dirs}")
    
    if len(class_dirs) == 0:
        print("❌ No class directories found!")
        return False
    
    # Check images in each class
    total_images = 0
    for class_dir in class_dirs:
        class_path = os.path.join(DATA_DIR, class_dir)
        images = [f for f in os.listdir(class_path) 
                 if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
        print(f"Class '{class_dir}': {len(images)} images")
        total_images += len(images)
        
        # List first few images
        for img in images[:3]:
            img_path = os.path.join(class_path, img)
            print(f"  - {img} (size: {os.path.getsize(img_path)} bytes)")
    
    print(f"Total images: {total_images}")
    
    if total_images == 0:
        print("❌ No images found!")
        return False
    
    # Try to load data using ProductDetector
    print("\n=== Testing ProductDetector ===")
    try:
        detector = ProductDetector(DATA_DIR, IMG_SIZE, BATCH_SIZE)
        X, y, class_names = detector.load_and_preprocess_data()
        
        print(f"✓ Data loaded successfully!")
        print(f"Images shape: {X.shape}")
        print(f"Labels shape: {y.shape}")
        print(f"Class names: {class_names}")
        print(f"Unique labels: {np.unique(y)}")
        
        # Check data distribution
        for i, class_name in enumerate(class_names):
            count = np.sum(y == i)
            print(f"Class '{class_name}': {count} images")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_data_loading()
    if success:
        print("\n✅ Data loading works correctly!")
    else:
        print("\n❌ Data loading failed!")
