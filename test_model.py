"""
Test the trained model with sample images from the dataset
"""

import os
import random
from production_predictor import ProductionPredictor
import json


def test_with_dataset_images():
    """Test the model with images from the dataset"""
    
    # Check if model exists
    model_path = 'product_detector_model.h5'
    if not os.path.exists(model_path):
        print("❌ Model not found! Please train the model first:")
        print("   python train_with_dataset.py")
        return
    
    # Check if config exists
    if not os.path.exists('model_config.json'):
        print("❌ Model configuration not found!")
        return
    
    print("🤖 Loading trained model...")
    predictor = ProductionPredictor(model_path)
    
    # Load model config to get class names
    with open('model_config.json', 'r') as f:
        config = json.load(f)
    
    print(f"✅ Model loaded successfully!")
    print(f"📊 Trained classes: {config['class_names']}")
    print(f"🖼️  Image size: {config['img_size']}")
    print()
    
    # Test with images from dataset
    dataset_dir = "dataset"
    test_results = []
    
    for class_name in config['class_names']:
        class_dir = os.path.join(dataset_dir, class_name)
        if os.path.exists(class_dir):
            # Get all images in this class
            images = [f for f in os.listdir(class_dir) 
                     if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
            
            if images:
                # Test with first image from each class
                test_image = os.path.join(class_dir, images[0])
                print(f"🔍 Testing with: {test_image}")
                
                result = predictor.predict(test_image, threshold=0.3)
                
                if 'error' in result:
                    print(f"❌ Error: {result['error']}")
                else:
                    print(f"📝 Predicted: {result['predicted_class']}")
                    print(f"🎯 Confidence: {result['confidence']:.4f}")
                    print(f"✅ Above threshold: {result['above_threshold']}")
                    
                    # Show top 3 predictions
                    sorted_preds = sorted(result['all_predictions'].items(), 
                                        key=lambda x: x[1], reverse=True)
                    print("📊 Top predictions:")
                    for i, (class_pred, prob) in enumerate(sorted_preds[:3]):
                        print(f"   {i+1}. {class_pred}: {prob:.4f}")
                    
                    test_results.append({
                        'true_class': class_name,
                        'predicted_class': result['predicted_class'],
                        'confidence': result['confidence'],
                        'correct': result['predicted_class'] == class_name
                    })
                
                print("-" * 50)
    
    # Summary
    if test_results:
        correct_predictions = sum(1 for r in test_results if r['correct'])
        accuracy = correct_predictions / len(test_results)
        avg_confidence = sum(r['confidence'] for r in test_results) / len(test_results)
        
        print("📈 TEST SUMMARY:")
        print(f"   Total tests: {len(test_results)}")
        print(f"   Correct predictions: {correct_predictions}")
        print(f"   Accuracy: {accuracy:.4f} ({accuracy*100:.1f}%)")
        print(f"   Average confidence: {avg_confidence:.4f}")
        
        print("\n🎉 Model testing complete!")
        print("💡 To test with your own images, use:")
        print("   python production_predictor.py")


def interactive_test():
    """Interactive testing mode"""
    model_path = 'product_detector_model.h5'
    if not os.path.exists(model_path):
        print("❌ Model not found! Please train the model first.")
        return
    
    predictor = ProductionPredictor(model_path)
    
    print("🔄 Interactive Testing Mode")
    print("Enter image path (or 'quit' to exit):")
    
    while True:
        image_path = input("\n📁 Image path: ").strip()
        
        if image_path.lower() in ['quit', 'exit', 'q']:
            break
        
        if not os.path.exists(image_path):
            print(f"❌ File not found: {image_path}")
            continue
        
        result = predictor.predict(image_path)
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
        else:
            print(f"📝 Predicted: {result['predicted_class']}")
            print(f"🎯 Confidence: {result['confidence']:.4f}")
            
            # Show all predictions
            print("📊 All predictions:")
            for class_name, prob in result['all_predictions'].items():
                print(f"   {class_name}: {prob:.4f}")


def main():
    """Main testing function"""
    print("=== Product Detector Testing ===\n")
    
    # Test with dataset images
    test_with_dataset_images()
    
    # Ask for interactive testing
    print("\n" + "="*50)
    response = input("🤔 Would you like to test with custom images? (y/n): ").strip().lower()
    if response in ['y', 'yes']:
        interactive_test()
    
    print("\n👋 Testing complete!")


if __name__ == "__main__":
    main()