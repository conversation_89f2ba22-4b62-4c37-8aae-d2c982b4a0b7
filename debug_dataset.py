"""
Debug script to check dataset and fix training issues
"""

import os
import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt


def check_dataset_structure():
    """Check the dataset structure and image files"""
    dataset_dir = "dataset"
    
    print("=== Dataset Structure Analysis ===")
    print(f"Dataset directory: {dataset_dir}")
    print(f"Exists: {os.path.exists(dataset_dir)}")
    print()
    
    if not os.path.exists(dataset_dir):
        print("❌ Dataset directory not found!")
        return False
    
    # Get class directories
    class_dirs = [d for d in os.listdir(dataset_dir) 
                  if os.path.isdir(os.path.join(dataset_dir, d))]
    
    print(f"Found {len(class_dirs)} class directories:")
    
    total_images = 0
    valid_images = 0
    
    for class_name in class_dirs:
        class_path = os.path.join(dataset_dir, class_name)
        print(f"\n📁 Class: {class_name}")
        print(f"   Path: {class_path}")
        
        # Get image files
        image_files = [f for f in os.listdir(class_path) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
        
        print(f"   Images found: {len(image_files)}")
        total_images += len(image_files)
        
        # Check each image
        for img_file in image_files:
            img_path = os.path.join(class_path, img_file)
            try:
                # Try to load with OpenCV
                img = cv2.imread(img_path)
                if img is not None:
                    h, w, c = img.shape
                    print(f"   ✅ {img_file}: {w}x{h}x{c}")
                    valid_images += 1
                else:
                    print(f"   ❌ {img_file}: Could not load with OpenCV")
                    
                    # Try with PIL
                    try:
                        pil_img = Image.open(img_path)
                        print(f"      📷 PIL can load: {pil_img.size}, mode: {pil_img.mode}")
                    except Exception as e:
                        print(f"      ❌ PIL error: {e}")
                        
            except Exception as e:
                print(f"   ❌ {img_file}: Error - {e}")
    
    print(f"\n📊 Summary:")
    print(f"   Total classes: {len(class_dirs)}")
    print(f"   Total images: {total_images}")
    print(f"   Valid images: {valid_images}")
    print(f"   Success rate: {valid_images/total_images*100:.1f}%" if total_images > 0 else "No images")
    
    return valid_images > 0


def test_image_loading():
    """Test loading and preprocessing images"""
    dataset_dir = "dataset"
    
    print("\n=== Testing Image Loading ===")
    
    # Find first image
    for class_name in os.listdir(dataset_dir):
        class_path = os.path.join(dataset_dir, class_name)
        if os.path.isdir(class_path):
            image_files = [f for f in os.listdir(class_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
            
            if image_files:
                test_image = os.path.join(class_path, image_files[0])
                print(f"Testing with: {test_image}")
                
                try:
                    # Load with OpenCV
                    img = cv2.imread(test_image)
                    if img is not None:
                        print(f"✅ OpenCV: {img.shape}")
                        
                        # Convert BGR to RGB
                        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        print(f"✅ BGR->RGB: {img_rgb.shape}")
                        
                        # Resize
                        img_resized = cv2.resize(img_rgb, (224, 224))
                        print(f"✅ Resized: {img_resized.shape}")
                        
                        # Normalize
                        img_norm = img_resized.astype(np.float32) / 255.0
                        print(f"✅ Normalized: {img_norm.shape}, range: {img_norm.min():.3f}-{img_norm.max():.3f}")
                        
                        # Show image
                        plt.figure(figsize=(10, 4))
                        plt.subplot(1, 2, 1)
                        plt.imshow(img_rgb)
                        plt.title(f"Original: {img_rgb.shape}")
                        plt.axis('off')
                        
                        plt.subplot(1, 2, 2)
                        plt.imshow(img_norm)
                        plt.title(f"Processed: {img_norm.shape}")
                        plt.axis('off')
                        
                        plt.tight_layout()
                        plt.savefig('debug_image_processing.png', dpi=150, bbox_inches='tight')
                        plt.show()
                        
                        return True
                        
                    else:
                        print("❌ OpenCV could not load image")
                        
                except Exception as e:
                    print(f"❌ Error: {e}")
                
                break
    
    return False


def create_minimal_dataset():
    """Create a minimal working dataset for testing"""
    print("\n=== Creating Minimal Test Dataset ===")
    
    # Create test images if dataset is too small
    test_dir = "test_dataset"
    os.makedirs(test_dir, exist_ok=True)
    
    classes = ["class_a", "class_b", "class_c"]
    
    for class_name in classes:
        class_dir = os.path.join(test_dir, class_name)
        os.makedirs(class_dir, exist_ok=True)
        
        # Create 5 synthetic images per class
        for i in range(5):
            # Create a simple colored image
            if class_name == "class_a":
                color = [255, 0, 0]  # Red
            elif class_name == "class_b":
                color = [0, 255, 0]  # Green
            else:
                color = [0, 0, 255]  # Blue
            
            # Add some noise for variation
            img = np.full((100, 100, 3), color, dtype=np.uint8)
            noise = np.random.randint(-30, 30, img.shape, dtype=np.int16)
            img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            # Save image
            img_path = os.path.join(class_dir, f"image_{i+1}.jpg")
            cv2.imwrite(img_path, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
    
    print(f"✅ Created test dataset in '{test_dir}' with {len(classes)} classes, 5 images each")
    return test_dir


def main():
    """Main debug function"""
    print("🔍 Dataset Debugging Tool")
    print("=" * 50)
    
    # Check original dataset
    if check_dataset_structure():
        print("\n✅ Dataset structure looks good!")
        
        # Test image loading
        if test_image_loading():
            print("\n✅ Image loading works!")
        else:
            print("\n❌ Image loading failed!")
    else:
        print("\n❌ Dataset issues detected!")
        
        # Create test dataset
        test_dir = create_minimal_dataset()
        print(f"\n💡 You can test with: python train_with_dataset.py --data_dir {test_dir}")
    
    print("\n" + "=" * 50)
    print("Debug complete!")


if __name__ == "__main__":
    main()