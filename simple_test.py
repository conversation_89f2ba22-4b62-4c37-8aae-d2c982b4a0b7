"""
Simple test script for the trained model
"""

import os
import json
import numpy as np
import cv2
import tensorflow as tf

def load_model_and_config():
    """Load the trained model and configuration"""
    model_path = 'product_detector_model.h5'
    config_path = 'model_config.json'
    
    if not os.path.exists(model_path):
        print("❌ Model file not found! Please train the model first.")
        return None, None
    
    if not os.path.exists(config_path):
        print("❌ Configuration file not found!")
        return None, None
    
    # Load model
    print("Loading trained model...")
    model = tf.keras.models.load_model(model_path)
    
    # Load config
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    print(f"✓ Model loaded successfully!")
    print(f"✓ Classes: {config['class_names']}")
    
    return model, config

def preprocess_image(image_path, img_size=(224, 224)):
    """Preprocess image for prediction"""
    try:
        img = cv2.imread(image_path)
        if img is None:
            return None
        
        # Convert BGR to RGB
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        # Resize
        img = cv2.resize(img, img_size)
        # Normalize
        img = img.astype(np.float32) / 255.0
        # Add batch dimension
        img = np.expand_dims(img, axis=0)
        
        return img
    except Exception as e:
        print(f"Error preprocessing image: {e}")
        return None

def predict_image(model, config, image_path):
    """Predict product class for an image"""
    img = preprocess_image(image_path, tuple(config['img_size']))
    if img is None:
        return None
    
    # Make prediction
    predictions = model.predict(img, verbose=0)
    predicted_class_idx = np.argmax(predictions[0])
    confidence = predictions[0][predicted_class_idx]
    
    predicted_class = config['class_names'][predicted_class_idx]
    
    return {
        'predicted_class': predicted_class,
        'confidence': float(confidence),
        'all_predictions': {
            config['class_names'][i]: float(predictions[0][i]) 
            for i in range(len(config['class_names']))
        }
    }

def test_with_dataset_images():
    """Test the model with images from the dataset"""
    model, config = load_model_and_config()
    if model is None:
        return
    
    dataset_dir = "dataset"
    if not os.path.exists(dataset_dir):
        print("❌ Dataset directory not found!")
        return
    
    print("\n=== Testing with Dataset Images ===")
    
    total_tests = 0
    correct_predictions = 0
    
    for class_name in config['class_names']:
        class_dir = os.path.join(dataset_dir, class_name)
        if os.path.exists(class_dir):
            # Get image files
            image_files = [f for f in os.listdir(class_dir) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            
            for img_file in image_files:
                img_path = os.path.join(class_dir, img_file)
                print(f"\n📸 Testing: {img_path}")
                
                result = predict_image(model, config, img_path)
                if result:
                    print(f"   True class: {class_name}")
                    print(f"   Predicted: {result['predicted_class']}")
                    print(f"   Confidence: {result['confidence']:.4f}")
                    
                    # Check if correct
                    is_correct = result['predicted_class'] == class_name
                    print(f"   Correct: {'✓' if is_correct else '✗'}")
                    
                    if is_correct:
                        correct_predictions += 1
                    total_tests += 1
                    
                    # Show top predictions
                    sorted_preds = sorted(result['all_predictions'].items(), 
                                        key=lambda x: x[1], reverse=True)
                    print("   Top predictions:")
                    for i, (pred_class, prob) in enumerate(sorted_preds[:3]):
                        print(f"     {i+1}. {pred_class}: {prob:.4f}")
                else:
                    print("   ❌ Failed to process image")
    
    # Summary
    if total_tests > 0:
        accuracy = correct_predictions / total_tests
        print(f"\n📊 Test Summary:")
        print(f"   Total tests: {total_tests}")
        print(f"   Correct: {correct_predictions}")
        print(f"   Accuracy: {accuracy:.4f} ({accuracy*100:.1f}%)")
    else:
        print("\n❌ No tests performed")

def interactive_test():
    """Interactive testing mode"""
    model, config = load_model_and_config()
    if model is None:
        return
    
    print("\n=== Interactive Testing Mode ===")
    print("Enter image path (or 'quit' to exit):")
    
    while True:
        image_path = input("\n📁 Image path: ").strip()
        
        if image_path.lower() in ['quit', 'exit', 'q']:
            break
        
        if not os.path.exists(image_path):
            print(f"❌ File not found: {image_path}")
            continue
        
        result = predict_image(model, config, image_path)
        if result:
            print(f"📝 Predicted: {result['predicted_class']}")
            print(f"🎯 Confidence: {result['confidence']:.4f}")
            print("📊 All predictions:")
            for class_name, prob in result['all_predictions'].items():
                print(f"   {class_name}: {prob:.4f}")
        else:
            print("❌ Failed to process image")

def main():
    """Main testing function"""
    print("=== Product Detector Testing ===")
    
    # Test with dataset images
    test_with_dataset_images()
    
    # Ask for interactive testing
    print("\n" + "="*50)
    response = input("🤔 Test with custom images? (y/n): ").strip().lower()
    if response in ['y', 'yes']:
        interactive_test()
    
    print("\n👋 Testing complete!")

if __name__ == "__main__":
    main()