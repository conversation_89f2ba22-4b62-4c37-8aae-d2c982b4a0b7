"""
Product Detection using CNN with TensorFlow/Keras
This module provides functionality to train a CNN model for product classification
and generate predictions with confidence statistics.
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
import pandas as pd
from PIL import Image
import json


class ProductDetector:
    def __init__(self, data_dir, img_size=(224, 224), batch_size=32):
        """
        Initialize the Product Detector
        
        Args:
            data_dir (str): Path to the root directory containing product folders
            img_size (tuple): Target image size for preprocessing
            batch_size (int): Batch size for training
        """
        self.data_dir = data_dir
        self.img_size = img_size
        self.batch_size = batch_size
        self.model = None
        self.class_names = []
        self.history = None
        
    def preprocess_image(self, image_path):
        """
        Preprocess a single image with uniform formatting
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            numpy.ndarray: Preprocessed image array
        """
        try:
            # Read image using OpenCV
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError(f"Could not read image: {image_path}")
            
            # Convert BGR to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Resize image
            img = cv2.resize(img, self.img_size)
            
            # Normalize pixel values to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            # Apply histogram equalization for better contrast
            img_yuv = cv2.cvtColor((img * 255).astype(np.uint8), cv2.COLOR_RGB2YUV)
            img_yuv[:,:,0] = cv2.equalizeHist(img_yuv[:,:,0])
            img = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2RGB).astype(np.float32) / 255.0
            
            return img
            
        except Exception as e:
            print(f"Error preprocessing image {image_path}: {str(e)}")
            return None
    
    def load_and_preprocess_data(self):
        """
        Load and preprocess all images from the data directory
        
        Returns:
            tuple: (X, y, class_names) - preprocessed images, labels, and class names
        """
        print("Loading and preprocessing data...")
        
        if not os.path.exists(self.data_dir):
            raise ValueError(f"Data directory does not exist: {self.data_dir}")
        
        # Get class names from subdirectories
        self.class_names = [d for d in os.listdir(self.data_dir) 
                           if os.path.isdir(os.path.join(self.data_dir, d))]
        self.class_names.sort()
        
        if len(self.class_names) == 0:
            raise ValueError("No product folders found in data directory")
        
        print(f"Found {len(self.class_names)} product classes: {self.class_names}")
        
        X = []
        y = []
        
        for class_idx, class_name in enumerate(self.class_names):
            class_dir = os.path.join(self.data_dir, class_name)
            image_files = [f for f in os.listdir(class_dir) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
            
            print(f"Processing {len(image_files)} images for class '{class_name}'...")
            
            for img_file in image_files:
                img_path = os.path.join(class_dir, img_file)
                preprocessed_img = self.preprocess_image(img_path)
                
                if preprocessed_img is not None:
                    X.append(preprocessed_img)
                    y.append(class_idx)
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"Loaded {len(X)} images with shape {X.shape}")
        return X, y, self.class_names
    
    def create_cnn_model(self, num_classes):
        """
        Create a CNN model for product classification
        
        Args:
            num_classes (int): Number of product classes
            
        Returns:
            tensorflow.keras.Model: Compiled CNN model
        """
        model = keras.Sequential([
            # First Convolutional Block
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=(*self.img_size, 3)),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Second Convolutional Block
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Third Convolutional Block
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Fourth Convolutional Block
            layers.Conv2D(256, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Global Average Pooling
            layers.GlobalAveragePooling2D(),
            
            # Dense Layers
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            # Output Layer
            layers.Dense(num_classes, activation='softmax')
        ])
        
        # Compile the model
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def train_model(self, X, y, validation_split=0.2, epochs=50):
        """
        Train the CNN model
        
        Args:
            X (numpy.ndarray): Training images
            y (numpy.ndarray): Training labels
            validation_split (float): Fraction of data to use for validation
            epochs (int): Number of training epochs
        """
        print("Training the model...")
        
        # Split data into training and validation sets
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=42, stratify=y
        )
        
        # Create the model
        num_classes = len(self.class_names)
        self.model = self.create_cnn_model(num_classes)
        
        print("Model Architecture:")
        self.model.summary()
        
        # Data augmentation
        datagen = ImageDataGenerator(
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            horizontal_flip=True,
            zoom_range=0.2,
            shear_range=0.2,
            fill_mode='nearest'
        )
        
        # Callbacks
        callbacks = [
            EarlyStopping(patience=10, restore_best_weights=True),
            ModelCheckpoint('best_model.h5', save_best_only=True),
            ReduceLROnPlateau(factor=0.2, patience=5)
        ]
        
        # Train the model
        # Ensure we have at least 1 step per epoch for small datasets
        steps_per_epoch = max(1, len(X_train) // self.batch_size)

        self.history = self.model.fit(
            datagen.flow(X_train, y_train, batch_size=self.batch_size),
            steps_per_epoch=steps_per_epoch,
            epochs=epochs,
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
    
    def evaluate_model(self, X_test, y_test):
        """
        Evaluate the trained model and show confidence statistics
        
        Args:
            X_test (numpy.ndarray): Test images
            y_test (numpy.ndarray): Test labels
        """
        if self.model is None:
            raise ValueError("Model not trained yet. Please train the model first.")
        
        print("Evaluating model...")
        
        # Make predictions
        predictions = self.model.predict(X_test)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y_test)
        print(f"Test Accuracy: {accuracy:.4f}")
        
        # Classification report
        print("\nClassification Report:")
        print(classification_report(y_test, predicted_classes, target_names=self.class_names))
        
        # Confusion matrix
        cm = confusion_matrix(y_test, predicted_classes)
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.class_names, yticklabels=self.class_names)
        plt.title('Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Confidence statistics
        confidence_scores = np.max(predictions, axis=1)
        print(f"\nConfidence Statistics:")
        print(f"Mean Confidence: {np.mean(confidence_scores):.4f}")
        print(f"Std Confidence: {np.std(confidence_scores):.4f}")
        print(f"Min Confidence: {np.min(confidence_scores):.4f}")
        print(f"Max Confidence: {np.max(confidence_scores):.4f}")
        
        # Plot confidence distribution
        plt.figure(figsize=(10, 6))
        plt.hist(confidence_scores, bins=30, alpha=0.7, edgecolor='black')
        plt.title('Distribution of Prediction Confidence Scores')
        plt.xlabel('Confidence Score')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        plt.savefig('confidence_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return accuracy, predictions, confidence_scores
    
    def plot_training_history(self):
        """Plot training history"""
        if self.history is None:
            print("No training history available.")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # Plot accuracy
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)
        
        # Plot loss
        ax2.plot(self.history.history['loss'], label='Training Loss')
        ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_model(self, model_path='product_detector_model.h5'):
        """
        Save the trained model to an H5 file
        
        Args:
            model_path (str): Path to save the model
        """
        if self.model is None:
            raise ValueError("Model not trained yet. Please train the model first.")
        
        self.model.save(model_path)
        
        # Save class names and configuration
        config = {
            'class_names': self.class_names,
            'img_size': self.img_size,
            'num_classes': len(self.class_names)
        }
        
        with open('model_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Model saved to {model_path}")
        print("Model configuration saved to model_config.json")
    
    def load_model(self, model_path='product_detector_model.h5'):
        """
        Load a trained model from an H5 file
        
        Args:
            model_path (str): Path to the saved model
        """
        if not os.path.exists(model_path):
            raise ValueError(f"Model file not found: {model_path}")
        
        self.model = keras.models.load_model(model_path)
        
        # Load configuration
        if os.path.exists('model_config.json'):
            with open('model_config.json', 'r') as f:
                config = json.load(f)
            self.class_names = config['class_names']
            self.img_size = tuple(config['img_size'])
        
        print(f"Model loaded from {model_path}")
    
    def predict_single_image(self, image_path):
        """
        Predict the product class for a single image
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            tuple: (predicted_class, confidence, all_predictions)
        """
        if self.model is None:
            raise ValueError("Model not loaded. Please load or train a model first.")
        
        # Preprocess the image
        img = self.preprocess_image(image_path)
        if img is None:
            raise ValueError(f"Could not preprocess image: {image_path}")
        
        # Add batch dimension
        img_batch = np.expand_dims(img, axis=0)
        
        # Make prediction
        predictions = self.model.predict(img_batch, verbose=0)
        predicted_class_idx = np.argmax(predictions[0])
        confidence = predictions[0][predicted_class_idx]
        
        predicted_class = self.class_names[predicted_class_idx]
        
        # Create detailed prediction results
        results = {
            'predicted_class': predicted_class,
            'confidence': float(confidence),
            'all_predictions': {
                self.class_names[i]: float(predictions[0][i]) 
                for i in range(len(self.class_names))
            }
        }
        
        return results


def main():
    """Main function to demonstrate the product detector"""
    # Configuration
    DATA_DIR = "product_images"  # Root folder containing product-wise image folders
    IMG_SIZE = (224, 224)
    BATCH_SIZE = 32
    EPOCHS = 50
    
    print("=== Product Detection System ===")
    print(f"Data directory: {DATA_DIR}")
    print(f"Image size: {IMG_SIZE}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Epochs: {EPOCHS}")
    print()
    
    # Create product detector instance
    detector = ProductDetector(DATA_DIR, IMG_SIZE, BATCH_SIZE)
    
    try:
        # Load and preprocess data
        X, y, class_names = detector.load_and_preprocess_data()
        
        # Split data for training and testing
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"Training set: {len(X_train)} images")
        print(f"Test set: {len(X_test)} images")
        print()
        
        # Train the model
        detector.train_model(X_train, y_train, epochs=EPOCHS)
        
        # Plot training history
        detector.plot_training_history()
        
        # Evaluate the model
        accuracy, predictions, confidence_scores = detector.evaluate_model(X_test, y_test)
        
        # Save the model
        detector.save_model()
        
        print("\n=== Training and Evaluation Complete ===")
        print(f"Final Test Accuracy: {accuracy:.4f}")
        print("Model saved as 'product_detector_model.h5'")
        print("Ready for production use!")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        print("Please ensure the data directory exists and contains product folders with images.")


if __name__ == "__main__":
    main()