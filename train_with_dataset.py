"""
Training script specifically configured for the existing dataset
"""

import os
import sys
from product_detector import ProductDetector
from sklearn.model_selection import train_test_split
import numpy as np


def main():
    """Train the model with the existing dataset"""
    # Use the existing dataset folder
    DATA_DIR = "product_images"  # Changed from "dataset" to "product_images"
    IMG_SIZE = (224, 224)
    BATCH_SIZE = 4   # Very small batch size for small dataset (10 images total)
    EPOCHS = 30      # Fewer epochs for small dataset
    
    print("=== Product Detection Training ===")
    print(f"Using dataset directory: {DATA_DIR}")
    print(f"Image size: {IMG_SIZE}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Epochs: {EPOCHS}")
    print()
    
    # Create product detector instance
    detector = ProductDetector(DATA_DIR, IMG_SIZE, BATCH_SIZE)
    
    try:
        # Load and preprocess data
        print("Loading data from dataset folder...")
        X, y, class_names = detector.load_and_preprocess_data()
        
        print(f"Found classes: {class_names}")
        print(f"Total images: {len(X)}")
        
        # Check if we have enough data
        if len(X) < 10:
            print("Warning: Very small dataset. Consider adding more images for better results.")
        
        # Split data for training and testing
        # Use smaller test size for small datasets
        test_size = min(0.3, max(0.1, 2/len(X)))  # At least 1 image for test, max 30%
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=y
        )
        
        print(f"Training set: {len(X_train)} images")
        print(f"Test set: {len(X_test)} images")
        print()
        
        # Train the model
        print("Starting training...")
        detector.train_model(X_train, y_train, validation_split=0.2, epochs=EPOCHS)
        
        # Plot training history
        print("Generating training history plots...")
        detector.plot_training_history()
        
        # Evaluate the model
        print("Evaluating model performance...")
        accuracy, predictions, confidence_scores = detector.evaluate_model(X_test, y_test)
        
        # Save the model
        print("Saving trained model...")
        detector.save_model('product_detector_model.h5')
        
        print("\n" + "="*50)
        print("TRAINING COMPLETE!")
        print("="*50)
        print(f"Final Test Accuracy: {accuracy:.4f}")
        print(f"Model saved as: product_detector_model.h5")
        print(f"Configuration saved as: model_config.json")
        print("\nGenerated files:")
        print("- product_detector_model.h5 (trained model)")
        print("- model_config.json (model configuration)")
        print("- confusion_matrix.png (evaluation plot)")
        print("- confidence_distribution.png (confidence analysis)")
        print("- training_history.png (training curves)")
        print("\nYour model is ready for production use!")
        print("Test it with: python test_model.py")
        
    except Exception as e:
        print(f"Error during training: {str(e)}")
        print("\nTroubleshooting tips:")
        print("1. Ensure dataset folder contains product subfolders")
        print("2. Check that image files are in supported formats (jpg, png, etc.)")
        print("3. Verify images are not corrupted")


if __name__ == "__main__":
    main()