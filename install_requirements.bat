@echo off
echo Installing required packages for Product Detector...
echo.

REM Try different Python commands
py -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        python3 -m pip install -r requirements.txt
        if %errorlevel% neq 0 (
            echo Error: Could not find Python installation
            echo Please ensure Python is installed and accessible
            pause
            exit /b 1
        )
    )
)

echo.
echo ✓ All packages installed successfully!
echo.
echo Next steps:
echo 1. Run: py train_with_dataset.py (to train the model)
echo 2. Run: py test_model.py (to test the model)
echo.
pause