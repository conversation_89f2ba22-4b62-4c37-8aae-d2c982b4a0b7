# Product Detector Setup Instructions

## Prerequisites

### 1. Install Python
If Python is not installed, download and install it from:
- **Official Python**: https://www.python.org/downloads/
- **Anaconda** (recommended for ML): https://www.anaconda.com/products/distribution

**Important**: During installation, check "Add Python to PATH"

### 2. Verify Python Installation
Open Command Prompt or PowerShell and run:
```bash
python --version
# OR
py --version
# OR
python3 --version
```

## Quick Setup

### Option 1: Using Batch File (Windows)
1. Double-click `install_requirements.bat`
2. Wait for packages to install
3. Follow the on-screen instructions

### Option 2: Manual Installation
1. Open Command Prompt in this folder
2. Install requirements:
   ```bash
   pip install -r requirements.txt
   ```

## Training Your Model

### Step 1: Prepare Your Dataset
Your dataset is already organized correctly:
```
dataset/
├── Prince Chololate SP/
│   ├── 1.jpg
│   └── 2.jpg
├── Prince HR/
│   └── 1.jpg
└── Zeera Plus SP/
    └── 1.jpg
```

### Step 2: Train the Model
```bash
python train_with_dataset.py
```

This will:
- Load and preprocess your images
- Train a CNN model
- Generate evaluation plots
- Save the trained model as `product_detector_model.h5`

### Step 3: Test the Model
```bash
python test_model.py
```

This will test the model with your dataset images and show accuracy statistics.

## Production Use

### Single Image Prediction
```python
from production_predictor import ProductionPredictor

predictor = ProductionPredictor('product_detector_model.h5')
result = predictor.predict('path/to/image.jpg')

print(f"Product: {result['predicted_class']}")
print(f"Confidence: {result['confidence']:.4f}")
```

### Batch Prediction
```python
results = predictor.batch_predict(['img1.jpg', 'img2.jpg', 'img3.jpg'])
for result in results:
    print(f"{result['image_path']}: {result['predicted_class']} ({result['confidence']:.4f})")
```

## Expected Output Files

After training, you'll get:
- `product_detector_model.h5` - Trained model for production
- `model_config.json` - Model configuration
- `confusion_matrix.png` - Model evaluation plot
- `confidence_distribution.png` - Confidence analysis
- `training_history.png` - Training progress plots

## Troubleshooting

### Common Issues:

1. **"Python not found"**
   - Install Python from python.org
   - Ensure "Add to PATH" was checked during installation
   - Restart Command Prompt/PowerShell

2. **"Module not found"**
   - Run: `pip install -r requirements.txt`
   - Try: `python -m pip install tensorflow keras opencv-python`

3. **"CUDA/GPU errors"**
   - The model will automatically use CPU if GPU is not available
   - For GPU acceleration, install CUDA-compatible TensorFlow

4. **"Not enough data"**
   - Add more images to each product folder (recommended: 50+ per class)
   - Current dataset is small but should work for testing

5. **"Memory errors"**
   - Reduce batch size in `train_with_dataset.py`
   - Close other applications to free up RAM

## Model Performance Tips

### For Better Accuracy:
1. **More Data**: Add 50-100+ images per product class
2. **Diverse Images**: Include different angles, lighting, backgrounds
3. **Balanced Dataset**: Similar number of images per class
4. **Quality Images**: Clear, well-lit, focused images

### For Faster Training:
1. **GPU**: Use NVIDIA GPU with CUDA support
2. **Smaller Images**: Reduce IMG_SIZE in config
3. **Fewer Epochs**: Reduce EPOCHS if overfitting occurs

## File Descriptions

- `product_detector.py` - Main training module with CNN architecture
- `production_predictor.py` - Production inference module
- `train_with_dataset.py` - Training script for your dataset
- `test_model.py` - Model testing and evaluation
- `requirements.txt` - Python package dependencies
- `setup_environment.py` - Environment setup script

## Next Steps

1. **Install Python** (if not already installed)
2. **Run** `install_requirements.bat` or manually install packages
3. **Train** the model with `python train_with_dataset.py`
4. **Test** the model with `python test_model.py`
5. **Use** in production with `production_predictor.py`

Your product detection system will be ready for production use!