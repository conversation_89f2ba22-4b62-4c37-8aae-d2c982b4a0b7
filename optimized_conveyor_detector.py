"""
Optimized Conveyor Belt Product Detection System
- Works with the optimized small dataset model
- Real-time product detection and counting
- Unknown product detection with configurable thresholds
"""

import os
import cv2
import numpy as np
import tensorflow as tf
import json
import time
from datetime import datetime


class OptimizedConveyorDetector:
    def __init__(self, model_path='optimized_product_detector.h5', config_path='optimized_model_config.json'):
        """
        Initialize optimized conveyor belt detector
        
        Args:
            model_path: Path to trained model
            config_path: Path to model configuration
        """
        self.model = None
        self.config = None
        self.class_names = []
        self.img_size = (128, 128)
        self.confidence_threshold = 0.6
        
        # Statistics
        self.product_counts = {}
        self.unknown_count = 0
        self.total_detections = 0
        self.session_start_time = datetime.now()
        
        # Load model and config
        self.load_model(model_path, config_path)
        
        # Initialize product counts
        for class_name in self.class_names:
            self.product_counts[class_name] = 0
    
    def load_model(self, model_path, config_path):
        """Load the trained model and configuration"""
        try:
            # Load configuration
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    self.config = json.load(f)
                
                self.class_names = self.config['class_names']
                self.img_size = tuple(self.config['img_size'])
                self.confidence_threshold = self.config.get('confidence_threshold', 0.6)
                
                print(f"✅ Configuration loaded:")
                print(f"   Classes: {self.class_names}")
                print(f"   Image size: {self.img_size}")
                print(f"   Confidence threshold: {self.confidence_threshold}")
            else:
                raise FileNotFoundError(f"Configuration file not found: {config_path}")
            
            # Load model
            if os.path.exists(model_path):
                self.model = tf.keras.models.load_model(model_path)
                print(f"✅ Optimized model loaded from: {model_path}")
            else:
                raise FileNotFoundError(f"Model file not found: {model_path}")
                
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            raise
    
    def preprocess_image(self, image):
        """
        Preprocess image with enhanced CLAHE processing
        
        Args:
            image: OpenCV image (BGR format) or image path
            
        Returns:
            Preprocessed image array
        """
        try:
            # Handle both image path and image array
            if isinstance(image, str):
                img = cv2.imread(image)
                if img is None:
                    raise ValueError(f"Could not load image: {image}")
            else:
                img = image.copy()
            
            # Convert BGR to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Resize to model input size
            img = cv2.resize(img, self.img_size)
            
            # Enhanced preprocessing with CLAHE
            lab = cv2.cvtColor(img, cv2.COLOR_RGB2LAB)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            lab[:,:,0] = clahe.apply(lab[:,:,0])
            img = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
            
            # Normalize to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            return img
            
        except Exception as e:
            print(f"Error preprocessing image: {e}")
            return None
    
    def detect_product(self, image):
        """
        Detect product in image with optimized processing
        
        Args:
            image: OpenCV image or image path
            
        Returns:
            dict: Detection result with product info
        """
        if self.model is None:
            raise ValueError("Model not loaded!")
        
        # Preprocess image
        processed_img = self.preprocess_image(image)
        if processed_img is None:
            return {
                'success': False,
                'error': 'Image preprocessing failed'
            }
        
        try:
            # Make prediction
            img_batch = np.expand_dims(processed_img, axis=0)
            predictions = self.model.predict(img_batch, verbose=0)[0]
            
            # Get top prediction
            predicted_idx = np.argmax(predictions)
            confidence = float(predictions[predicted_idx])
            
            # Determine if product is known
            is_known_product = confidence >= self.confidence_threshold
            
            if is_known_product:
                predicted_class = self.class_names[predicted_idx]
                status = "DETECTED"
            else:
                predicted_class = "PRODUCT_NOT_FOUND"
                status = "UNKNOWN"
            
            # Create probability dictionary
            all_probabilities = {
                self.class_names[i]: float(predictions[i]) 
                for i in range(len(self.class_names))
            }
            
            # Update statistics
            self.update_statistics(predicted_class, is_known_product)
            
            return {
                'success': True,
                'product': predicted_class,
                'confidence': confidence,
                'status': status,
                'is_known_product': is_known_product,
                'all_probabilities': all_probabilities,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Prediction failed: {str(e)}'
            }
    
    def update_statistics(self, predicted_class, is_known_product):
        """Update detection statistics"""
        self.total_detections += 1
        
        if is_known_product:
            self.product_counts[predicted_class] += 1
        else:
            self.unknown_count += 1
    
    def get_statistics(self):
        """Get current detection statistics"""
        session_duration = datetime.now() - self.session_start_time
        
        return {
            'session_duration': str(session_duration),
            'total_detections': self.total_detections,
            'product_counts': self.product_counts.copy(),
            'unknown_count': self.unknown_count,
            'detection_rate': self.total_detections / max(1, session_duration.total_seconds()) * 60,  # per minute
            'known_product_rate': (self.total_detections - self.unknown_count) / max(1, self.total_detections) * 100
        }
    
    def reset_statistics(self):
        """Reset all statistics"""
        for class_name in self.class_names:
            self.product_counts[class_name] = 0
        self.unknown_count = 0
        self.total_detections = 0
        self.session_start_time = datetime.now()
        print("📊 Statistics reset")
    
    def print_statistics(self):
        """Print current statistics"""
        stats = self.get_statistics()
        
        print("\n" + "="*50)
        print("📊 OPTIMIZED CONVEYOR BELT STATISTICS")
        print("="*50)
        print(f"Session Duration: {stats['session_duration']}")
        print(f"Total Detections: {stats['total_detections']}")
        print(f"Detection Rate: {stats['detection_rate']:.1f} per minute")
        print(f"Known Product Rate: {stats['known_product_rate']:.1f}%")
        print()
        
        print("Product Counts:")
        for product, count in stats['product_counts'].items():
            print(f"  {product}: {count}")
        
        print(f"Unknown Products: {stats['unknown_count']}")
        print("="*50)
    
    def adjust_confidence_threshold(self, new_threshold):
        """Adjust confidence threshold dynamically"""
        old_threshold = self.confidence_threshold
        self.confidence_threshold = new_threshold
        print(f"🎯 Confidence threshold adjusted: {old_threshold:.2f} → {new_threshold:.2f}")
        
        # Update config
        if self.config:
            self.config['confidence_threshold'] = new_threshold
            with open('optimized_model_config.json', 'w') as f:
                json.dump(self.config, f, indent=2)
            print("✅ Configuration updated")


def test_optimized_system():
    """Test the optimized detection system"""
    print("🧪 Testing Optimized Conveyor Belt System")
    print("-" * 40)
    
    try:
        # Initialize detector
        detector = OptimizedConveyorDetector()
        
        # Test with sample images from dataset
        test_dir = "product_images"
        if not os.path.exists(test_dir):
            print(f"❌ Test directory not found: {test_dir}")
            return
        
        print("\n🔍 Testing with known products...")
        
        # Test each product class
        for class_name in os.listdir(test_dir):
            class_path = os.path.join(test_dir, class_name)
            if os.path.isdir(class_path):
                images = [f for f in os.listdir(class_path) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                
                if images:
                    # Test first image from this class
                    test_image = os.path.join(class_path, images[0])
                    print(f"\n📦 Testing: {class_name}")
                    
                    result = detector.detect_product(test_image)
                    
                    if result['success']:
                        print(f"   ✅ Detected: {result['product']}")
                        print(f"   📊 Confidence: {result['confidence']:.4f}")
                        print(f"   🎯 Status: {result['status']}")
                        
                        # Check accuracy
                        if result['is_known_product']:
                            if result['product'] in class_name or class_name in result['product']:
                                print(f"   ✅ CORRECT DETECTION!")
                            else:
                                print(f"   ⚠️  MISCLASSIFICATION")
                        else:
                            print(f"   ❌ FALSE NEGATIVE (should be detected)")
                    else:
                        print(f"   ❌ Detection failed: {result.get('error', 'Unknown error')}")
        
        # Test unknown product detection
        print("\n\n🎲 Testing unknown product detection...")
        random_image = np.random.randint(0, 255, (128, 128, 3), dtype=np.uint8)
        
        result = detector.detect_product(random_image)
        if result['success']:
            print(f"   🔍 Result: {result['product']}")
            print(f"   📊 Confidence: {result['confidence']:.4f}")
            
            if result['product'] == "PRODUCT_NOT_FOUND":
                print(f"   ✅ CORRECT: Unknown properly detected!")
            else:
                print(f"   ⚠️  WARNING: Random noise classified as product")
        
        # Show final statistics
        detector.print_statistics()
        
        print("\n✅ Optimized system test completed!")
        
        # Test threshold adjustment
        print("\n🎯 Testing threshold adjustment...")
        detector.adjust_confidence_threshold(0.8)
        detector.adjust_confidence_threshold(0.6)  # Reset
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main function for optimized conveyor belt detector"""
    print("🏭 Optimized Conveyor Belt Product Detection System")
    print("="*55)
    
    try:
        # Check if optimized model exists
        if not os.path.exists('optimized_product_detector.h5'):
            print("❌ Optimized model not found!")
            print("Please run training first: python optimized_small_dataset_detector.py")
            return
        
        print("✅ Optimized model found!")
        print("\n🚀 System Features:")
        print("- Optimized for small datasets")
        print("- Enhanced preprocessing with CLAHE")
        print("- Configurable confidence thresholds")
        print("- Real-time detection and counting")
        print("- Unknown product detection")
        
        # Run test
        test_optimized_system()
        
        print("\n🎉 Ready for conveyor belt deployment!")
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")


if __name__ == "__main__":
    main()
